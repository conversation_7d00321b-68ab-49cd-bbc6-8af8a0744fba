<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Aifrobeats</title>
    <link rel="icon" href="images/mlogo.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="min-h-screen" style="background-color: var(--bg-primary);">
    
    <!-- Navigation Header -->
    <header class="shadow-sm sticky top-0 z-50" style="background-color: var(--header-bg);">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.html" class="logo-container text-2xl font-bold text-transparent flex items-center">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="site-logo">
                    <span class="animated-gradient-text">Aifrobeats</span>
                </a>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2 bg-green-100 px-3 py-1 rounded-full">
                    <i class="fas fa-coins text-green-600"></i>
                    <span id="user-credits" class="font-semibold text-green-700">0</span>
                    <span class="text-green-600 text-sm">credits</span>
                </div>
                <span id="user-email" class="text-gray-600 hidden md:block"></span>
                <button id="logout-btn" class="text-red-600 hover:text-red-700 transition-colors">
                    <i class="fas fa-sign-out-alt mr-1"></i>Logout
                </button>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        
        <!-- Welcome Section -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold theme-text mb-2">Welcome to Your Music Dashboard!</h1>
            <p class="theme-text-muted">Manage your song requests and discover new music</p>
        </div>

        <!-- Quick Actions -->
        <div class="grid md:grid-cols-3 gap-6 mb-8">
            <div class="card text-center">
                <div class="feature-icon mx-auto mb-4">
                    <i class="fas fa-plus-circle"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2 theme-text">Request New Song</h3>
                <p class="theme-text-muted mb-4">Create a custom Afrobeats track</p>
                <a href="request-form.html" class="primary-button inline-flex items-center">
                    <i class="fas fa-music mr-2"></i>Start Request
                </a>
            </div>
            <div class="card text-center">
                <div class="feature-icon mx-auto mb-4">
                    <i class="fas fa-headphones"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2 theme-text">Browse Library</h3>
                <p class="theme-text-muted mb-4">Listen to our music collection</p>
                <a href="music-library.html" class="secondary-button inline-flex items-center">
                    <i class="fas fa-library-music mr-2"></i>Browse Music
                </a>
            </div>
            <div class="card text-center">
                <div class="feature-icon mx-auto mb-4">
                    <i class="fas fa-credit-card"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2 theme-text">Buy More Credits</h3>
                <p class="theme-text-muted mb-4">Get additional preview credits</p>
                <button class="secondary-button inline-flex items-center" onclick="buyCredits()">
                    <i class="fas fa-shopping-cart mr-2"></i>Buy Credits
                </button>
            </div>
        </div>

        <!-- My Requests Section -->
        <div class="card">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold theme-text">My Song Requests</h2>
                <button id="refresh-requests" class="text-green-600 hover:text-green-700 transition-colors">
                    <i class="fas fa-sync-alt mr-1"></i>Refresh
                </button>
            </div>
            
            <!-- Requests List -->
            <div id="requests-container">
                <!-- Loading State -->
                <div id="requests-loading" class="text-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
                    <p class="theme-text-muted">Loading your requests...</p>
                </div>
                
                <!-- Empty State -->
                <div id="requests-empty" class="text-center py-8 hidden">
                    <div class="text-6xl text-gray-300 mb-4">
                        <i class="fas fa-music"></i>
                    </div>
                    <h3 class="text-xl font-semibold theme-text mb-2">No requests yet</h3>
                    <p class="theme-text-muted mb-4">Start by creating your first custom song request</p>
                    <a href="request-form.html" class="primary-button inline-flex items-center">
                        <i class="fas fa-plus mr-2"></i>Create First Request
                    </a>
                </div>
                
                <!-- Requests List -->
                <div id="requests-list" class="space-y-4 hidden">
                    <!-- Requests will be populated here -->
                </div>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
            <span class="text-gray-700">Loading...</span>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>

    <!-- Dashboard Script -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCRlkgzE2FMFFywISBm4GO4pfZ5pUFql0o",
            authDomain: "aifrobeats-com.firebaseapp.com",
            projectId: "aifrobeats-com",
            storageBucket: "aifrobeats-com.firebasestorage.app",
            messagingSenderId: "1013326896271",
            appId: "1:1013326896271:web:95074e13e4d40adaa164fa",
            measurementId: "G-YCQLRG8L44"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        let currentUser = null;
        let requestsListener = null;

        // Auth state observer
        auth.onAuthStateChanged((user) => {
            if (user) {
                currentUser = user;
                document.getElementById('user-email').textContent = user.email;
                loadUserData();
                loadUserRequests();
            } else {
                // User is not signed in, redirect to login
                window.location.href = 'login.html';
            }
        });

        // Load user data
        async function loadUserData() {
            try {
                const userDoc = await db.collection('users').doc(currentUser.uid).get();
                if (userDoc.exists) {
                    const userData = userDoc.data();
                    document.getElementById('user-credits').textContent = userData.credits || 0;
                }
            } catch (error) {
                console.error('Error loading user data:', error);
            }
        }

        // Load user requests
        function loadUserRequests() {
            const requestsContainer = document.getElementById('requests-container');
            const loadingDiv = document.getElementById('requests-loading');
            const emptyDiv = document.getElementById('requests-empty');
            const listDiv = document.getElementById('requests-list');

            // Show loading
            loadingDiv.classList.remove('hidden');
            emptyDiv.classList.add('hidden');
            listDiv.classList.add('hidden');

            // Listen to user's requests
            requestsListener = db.collection('requests')
                .where('userId', '==', currentUser.uid)
                .orderBy('createdAt', 'desc')
                .onSnapshot((querySnapshot) => {
                    loadingDiv.classList.add('hidden');
                    
                    if (querySnapshot.empty) {
                        emptyDiv.classList.remove('hidden');
                        listDiv.classList.add('hidden');
                    } else {
                        emptyDiv.classList.add('hidden');
                        listDiv.classList.remove('hidden');
                        
                        // Clear existing requests
                        listDiv.innerHTML = '';
                        
                        // Add each request
                        querySnapshot.forEach((doc) => {
                            const request = doc.data();
                            const requestElement = createRequestElement(doc.id, request);
                            listDiv.appendChild(requestElement);
                        });
                    }
                }, (error) => {
                    console.error('Error loading requests:', error);
                    loadingDiv.classList.add('hidden');
                    emptyDiv.classList.remove('hidden');
                });
        }

        // Create request element
        function createRequestElement(id, request) {
            const div = document.createElement('div');
            div.className = 'bg-gray-50 rounded-lg p-4 border';
            
            const statusColor = {
                'pending': 'bg-yellow-100 text-yellow-800',
                'processing': 'bg-blue-100 text-blue-800',
                'completed': 'bg-green-100 text-green-800',
                'cancelled': 'bg-red-100 text-red-800'
            };

            const createdAt = request.createdAt ? new Date(request.createdAt.toDate()).toLocaleDateString() : 'Unknown';
            
            div.innerHTML = `
                <div class="flex justify-between items-start mb-3">
                    <h3 class="font-semibold text-lg theme-text">${request.songTitle || 'Custom Song Request'}</h3>
                    <span class="px-2 py-1 rounded-full text-xs font-semibold ${statusColor[request.status] || statusColor['pending']}">
                        ${request.status || 'pending'}
                    </span>
                </div>
                <p class="theme-text-muted text-sm mb-3">${request.description || 'No description provided'}</p>
                <div class="flex justify-between items-center text-sm theme-text-muted">
                    <span>Requested: ${createdAt}</span>
                    ${request.previewUrl ? `<a href="${request.previewUrl}" target="_blank" class="text-green-600 hover:text-green-700 font-semibold">
                        <i class="fas fa-play mr-1"></i>Listen to Preview
                    </a>` : ''}
                </div>
            `;
            
            return div;
        }

        // Logout function
        document.getElementById('logout-btn').addEventListener('click', async () => {
            try {
                await auth.signOut();
                window.location.href = 'index.html';
            } catch (error) {
                console.error('Error signing out:', error);
            }
        });

        // Refresh requests
        document.getElementById('refresh-requests').addEventListener('click', () => {
            loadUserRequests();
        });

        // Buy credits function (placeholder)
        function buyCredits() {
            alert('Credit purchase feature coming soon! Contact support for now.');
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (requestsListener) {
                requestsListener();
            }
        });
    </script>
</body>
</html>
