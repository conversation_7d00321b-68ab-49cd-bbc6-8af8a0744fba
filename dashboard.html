<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Aifrobeats</title>
    <link rel="icon" href="images/mlogo.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="min-h-screen" style="background-color: var(--bg-primary);">
    
    <!-- Navigation Header -->
    <header class="shadow-sm sticky top-0 z-50" style="background-color: var(--header-bg);">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.html" class="logo-container text-2xl font-bold text-transparent flex items-center">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="site-logo">
                    <span class="animated-gradient-text">Aifrobeats</span>
                </a>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2 bg-green-100 px-3 py-1 rounded-full">
                    <i class="fas fa-coins text-green-600"></i>
                    <span id="user-credits" class="font-semibold text-green-700">0</span>
                    <span class="text-green-600 text-sm">credits</span>
                </div>
                <span id="user-email" class="text-gray-600 hidden md:block"></span>
                <button id="logout-btn" class="text-red-600 hover:text-red-700 transition-colors">
                    <i class="fas fa-sign-out-alt mr-1"></i>Logout
                </button>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        
        <!-- Welcome Section -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold theme-text mb-2">Welcome to Your Music Dashboard!</h1>
            <p class="theme-text-muted">Manage your song requests and discover new music</p>
        </div>

        <!-- Quick Actions -->
        <div class="grid md:grid-cols-3 gap-6 mb-8">
            <div class="card text-center">
                <div class="feature-icon mx-auto mb-4">
                    <i class="fas fa-plus-circle"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2 theme-text">Request New Song</h3>
                <p class="theme-text-muted mb-4">Create a custom Afrobeats track</p>
                <a href="request-form.html" class="primary-button inline-flex items-center">
                    <i class="fas fa-music mr-2"></i>Start Request
                </a>
            </div>
            <div class="card text-center">
                <div class="feature-icon mx-auto mb-4">
                    <i class="fas fa-headphones"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2 theme-text">Browse Library</h3>
                <p class="theme-text-muted mb-4">Listen to our music collection</p>
                <a href="music-library.html" class="secondary-button inline-flex items-center">
                    <i class="fas fa-library-music mr-2"></i>Browse Music
                </a>
            </div>
            <div class="card text-center">
                <div class="feature-icon mx-auto mb-4">
                    <i class="fas fa-credit-card"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2 theme-text">Buy More Credits</h3>
                <p class="theme-text-muted mb-4">Get additional preview credits</p>
                <button class="secondary-button inline-flex items-center" onclick="buyCredits()">
                    <i class="fas fa-shopping-cart mr-2"></i>Buy Credits
                </button>
            </div>
        </div>

        <!-- My Requests Section -->
        <div class="card">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold theme-text">My Song Requests</h2>
                <button id="refresh-requests" class="text-green-600 hover:text-green-700 transition-colors">
                    <i class="fas fa-sync-alt mr-1"></i>Refresh
                </button>
            </div>
            
            <!-- Requests List -->
            <div id="requests-container">
                <!-- Loading State -->
                <div id="requests-loading" class="text-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
                    <p class="theme-text-muted">Loading your requests...</p>
                </div>
                
                <!-- Empty State -->
                <div id="requests-empty" class="text-center py-8 hidden">
                    <div class="text-6xl text-gray-300 mb-4">
                        <i class="fas fa-music"></i>
                    </div>
                    <h3 class="text-xl font-semibold theme-text mb-2">No requests yet</h3>
                    <p class="theme-text-muted mb-4">Start by creating your first custom song request</p>
                    <a href="request-form.html" class="primary-button inline-flex items-center">
                        <i class="fas fa-plus mr-2"></i>Create First Request
                    </a>
                </div>
                
                <!-- Requests List -->
                <div id="requests-list" class="space-y-4 hidden">
                    <!-- Requests will be populated here -->
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer id="contact" class="site-footer bg-gray-900 text-gray-400 py-12 md:py-16">
        <div class="container mx-auto px-4 md:px-6 footer-container">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-10">
                <!-- Company Info -->
                <div class="footer-col">
                    <h3 class="text-xl md:text-2xl font-semibold text-white mb-4 flex items-center">
                        <img src="images/mlogo.png" alt="Aifrobeats Logo" class="w-8 h-8 mr-2">
                        <span class="animated-gradient-text">Aifrobeats</span>
                    </h3>
                    <p class="mb-4 text-sm md:text-base">AI-Generated Afrobeats, Human-Curated for you. Fast, affordable, and unique music for every occasion.</p>
                    <div class="flex items-center mb-3">
                        <i class="fas fa-envelope text-green-500 mr-3"></i>
                        <a href="mailto:<EMAIL>" class="hover:text-green-400 transition-colors"><EMAIL></a>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-phone-alt text-green-500 mr-3"></i>
                        <a href="tel:+2347038808350" class="hover:text-green-400 transition-colors">+234 ************</a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="footer-col">
                    <h3 class="text-lg md:text-xl font-semibold text-white mb-4">Quick Links</h3>
                    <ul class="space-y-2 footer-links">
                        <li>
                            <a href="index.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Home
                            </a>
                        </li>
                        <li>
                            <a href="dashboard.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Dashboard
                            </a>
                        </li>
                        <li>
                            <a href="music-library.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Music Library
                            </a>
                        </li>
                        <li>
                            <a href="request-form.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Request Song
                            </a>
                        </li>
                        <li>
                            <a href="index.html#pricing" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Pricing
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Connect With Us -->
                <div class="footer-col">
                    <h3 class="text-lg md:text-xl font-semibold text-white mb-4">Connect With Us</h3>
                    <p class="mb-3 text-sm md:text-base">Follow us for updates and examples!</p>
                    <div class="flex space-x-3 mb-5">
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-soundcloud"></i>
                        </a>
                    </div>
                    <div class="bg-gray-800 p-3 rounded-lg newsletter-form">
                        <h4 class="text-white text-sm font-semibold mb-2">Subscribe to our newsletter</h4>
                        <form class="flex">
                            <input type="email" placeholder="Your email" class="form-input bg-gray-700 border-0 text-white text-sm rounded-l-lg focus:ring-green-500 focus:border-green-500 flex-grow">
                            <button type="submit" class="bg-green-600 text-white px-3 rounded-r-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Copyright -->
            <div class="mt-6 md:mt-8 pt-5 md:pt-6 border-t border-gray-700 text-center text-xs md:text-sm">
                <p>&copy; <span id="currentYear"></span> AIFROBEATS CUSTOM MUSIC PRODUCTIONS – All Rights Reserved.</p>
                <div class="mt-2 flex flex-wrap justify-center gap-4">
                    <a href="terms.html" class="hover:text-green-400 transition-colors">Terms of Service</a>
                    <span class="hidden md:inline">|</span>
                    <a href="privacy.html" class="hover:text-green-400 transition-colors">Privacy Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
            <span class="text-gray-700">Loading...</span>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>

    <!-- Dashboard Script -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCRlkgzE2FMFFywISBm4GO4pfZ5pUFql0o",
            authDomain: "aifrobeats-com.firebaseapp.com",
            projectId: "aifrobeats-com",
            storageBucket: "aifrobeats-com.firebasestorage.app",
            messagingSenderId: "1013326896271",
            appId: "1:1013326896271:web:95074e13e4d40adaa164fa",
            measurementId: "G-YCQLRG8L44"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        let currentUser = null;
        let requestsListener = null;

        // Auth state observer
        auth.onAuthStateChanged((user) => {
            if (user) {
                currentUser = user;
                document.getElementById('user-email').textContent = user.email;
                console.log('User authenticated:', user.email);
                loadUserData();
                loadUserRequests();
            } else {
                // User is not signed in, redirect to login
                console.log('User not authenticated, redirecting to login');
                window.location.href = 'login.html';
            }
        });

        // Load user data
        async function loadUserData() {
            try {
                const userDoc = await db.collection('users').doc(currentUser.uid).get();
                if (userDoc.exists) {
                    const userData = userDoc.data();
                    const credits = userData.credits || 0;
                    document.getElementById('user-credits').textContent = credits;
                    console.log('User credits loaded:', credits);
                } else {
                    // User document doesn't exist, create it with 3 credits
                    console.log('User document not found, creating with 3 credits');
                    await db.collection('users').doc(currentUser.uid).set({
                        email: currentUser.email,
                        credits: 3,
                        createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                        requests: []
                    });
                    document.getElementById('user-credits').textContent = '3';
                }
            } catch (error) {
                console.error('Error loading user data:', error);
                // Show error message to user
                alert('Error loading your account data. Please refresh the page.');
            }
        }

        // Load user requests
        function loadUserRequests() {
            const requestsContainer = document.getElementById('requests-container');
            const loadingDiv = document.getElementById('requests-loading');
            const emptyDiv = document.getElementById('requests-empty');
            const listDiv = document.getElementById('requests-list');

            // Show loading
            loadingDiv.classList.remove('hidden');
            emptyDiv.classList.add('hidden');
            listDiv.classList.add('hidden');

            // Listen to user's requests
            requestsListener = db.collection('requests')
                .where('userId', '==', currentUser.uid)
                .orderBy('createdAt', 'desc')
                .onSnapshot((querySnapshot) => {
                    loadingDiv.classList.add('hidden');
                    
                    if (querySnapshot.empty) {
                        emptyDiv.classList.remove('hidden');
                        listDiv.classList.add('hidden');
                    } else {
                        emptyDiv.classList.add('hidden');
                        listDiv.classList.remove('hidden');
                        
                        // Clear existing requests
                        listDiv.innerHTML = '';
                        
                        // Add each request
                        querySnapshot.forEach((doc) => {
                            const request = doc.data();
                            const requestElement = createRequestElement(doc.id, request);
                            listDiv.appendChild(requestElement);
                        });
                    }
                }, (error) => {
                    console.error('Error loading requests:', error);
                    loadingDiv.classList.add('hidden');
                    emptyDiv.classList.remove('hidden');
                });
        }

        // Create request element
        function createRequestElement(id, request) {
            const div = document.createElement('div');
            div.className = 'bg-gray-50 rounded-lg p-4 border';
            
            const statusColor = {
                'pending': 'bg-yellow-100 text-yellow-800',
                'processing': 'bg-blue-100 text-blue-800',
                'completed': 'bg-green-100 text-green-800',
                'cancelled': 'bg-red-100 text-red-800'
            };

            const createdAt = request.createdAt ? new Date(request.createdAt.toDate()).toLocaleDateString() : 'Unknown';
            
            div.innerHTML = `
                <div class="flex justify-between items-start mb-3">
                    <h3 class="font-semibold text-lg theme-text">${request.songTitle || 'Custom Song Request'}</h3>
                    <span class="px-2 py-1 rounded-full text-xs font-semibold ${statusColor[request.status] || statusColor['pending']}">
                        ${request.status || 'pending'}
                    </span>
                </div>
                <p class="theme-text-muted text-sm mb-3">${request.description || 'No description provided'}</p>
                <div class="flex justify-between items-center text-sm theme-text-muted">
                    <span>Requested: ${createdAt}</span>
                    ${request.previewUrl ? `<a href="${request.previewUrl}" target="_blank" class="text-green-600 hover:text-green-700 font-semibold">
                        <i class="fas fa-play mr-1"></i>Listen to Preview
                    </a>` : ''}
                </div>
            `;
            
            return div;
        }

        // Logout function
        document.getElementById('logout-btn').addEventListener('click', async () => {
            try {
                await auth.signOut();
                window.location.href = 'index.html';
            } catch (error) {
                console.error('Error signing out:', error);
            }
        });

        // Refresh requests
        document.getElementById('refresh-requests').addEventListener('click', () => {
            loadUserRequests();
        });

        // Buy credits function (placeholder)
        function buyCredits() {
            alert('Credit purchase feature coming soon! Contact support for now.');
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (requestsListener) {
                requestsListener();
            }
        });

        // Set current year in footer
        document.getElementById('currentYear').textContent = new Date().getFullYear();
    </script>
</body>
</html>
