<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ditch Studio Fees, Get Your Jingles and Songs for Cheap!</title>
    <link rel="icon" href="images/mlogo.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
</head>
<body>

    <header class="shadow-sm sticky top-0 z-50" style="background-color: var(--header-bg); transition: background-color 0.3s ease;">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <a href="#" class="logo-container text-2xl font-bold text-transparent flex items-center">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="site-logo">
                    <span class="animated-gradient-text">Aifrobeats</span>
                </a>
            </div>
            <div class="hidden md:flex space-x-6 items-center">
                <a href="#home" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Home</a>
                <a href="#how-it-works" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">How It Works</a>
                <a href="#examples" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Our Music</a>
                <a href="music-library.html" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Music Library</a>
                <a href="#use-cases" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Use Cases</a>
                <a href="#pricing" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Pricing</a>
                <a href="#faq" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">FAQ</a>

                <!-- Auth buttons - shown when not logged in -->
                <div id="auth-buttons" class="flex items-center space-x-3">
                    <a href="login.html" class="text-green-600 hover:text-green-700 font-semibold transition-colors">
                        <i class="fas fa-sign-in-alt mr-1"></i>Login
                    </a>
                    <a href="signup.html" class="primary-button flex items-center">
                        <i class="fas fa-user-plus mr-2"></i>Sign Up Free
                    </a>
                </div>

                <!-- User menu - shown when logged in -->
                <div id="user-menu" class="hidden flex items-center space-x-3">
                    <div class="flex items-center space-x-2 bg-green-100 px-3 py-1 rounded-full">
                        <i class="fas fa-coins text-green-600"></i>
                        <span id="nav-user-credits" class="font-semibold text-green-700">0</span>
                        <span class="text-green-600 text-sm">credits</span>
                    </div>
                    <a href="dashboard.html" class="primary-button flex items-center">
                        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                    </a>
                    <button id="nav-logout-btn" class="text-red-600 hover:text-red-700 transition-colors">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>

                <div class="theme-toggle ml-4" id="themeToggle" title="Toggle dark/light mode">
                    <i class="fas fa-moon"></i>
                </div>
            </div>
            <div class="md:hidden flex items-center">
                <div class="theme-toggle mr-4" id="mobileThemeToggle" title="Toggle dark/light mode">
                    <i class="fas fa-moon"></i>
                </div>
                <button id="mobile-menu-button" style="color: var(--text-primary);" class="focus:outline-none hamburger-menu-button">
                    <span class="hamburger-icon"></span>
                </button>
            </div>
        </nav>
        <div id="mobile-menu" class="hidden md:hidden px-6 pb-6 space-y-4 border-t theme-bg mobile-menu-container" style="border-color: var(--border-color); transition: background-color 0.3s ease;">
            <a href="#home" class="block py-3 hover:text-green-600 transition-colors theme-text text-lg font-medium">Home</a>
            <a href="#how-it-works" class="block py-3 hover:text-green-600 transition-colors theme-text text-lg font-medium">How It Works</a>
            <a href="music-library.html" class="block py-3 hover:text-green-600 transition-colors theme-text text-lg font-medium">Music Library</a>

            <!-- Mobile Auth buttons - shown when not logged in -->
            <div id="mobile-auth-buttons" class="space-y-3 pt-2">
                <a href="login.html" class="block py-3 text-center text-green-600 hover:text-green-700 font-semibold transition-colors">
                    <i class="fas fa-sign-in-alt mr-2"></i>Login
                </a>
                <a href="signup.html" class="primary-button w-full text-center flex items-center justify-center">
                    <i class="fas fa-user-plus mr-2"></i>Sign Up Free
                </a>
            </div>

            <!-- Mobile User menu - shown when logged in -->
            <div id="mobile-user-menu" class="hidden space-y-3 pt-2">
                <div class="flex items-center justify-center space-x-2 bg-green-100 px-3 py-2 rounded-full">
                    <i class="fas fa-coins text-green-600"></i>
                    <span id="mobile-nav-user-credits" class="font-semibold text-green-700">0</span>
                    <span class="text-green-600 text-sm">credits</span>
                </div>
                <a href="dashboard.html" class="primary-button w-full text-center flex items-center justify-center">
                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                </a>
                <button id="mobile-nav-logout-btn" class="w-full py-3 text-center text-red-600 hover:text-red-700 transition-colors">
                    <i class="fas fa-sign-out-alt mr-2"></i>Logout
                </button>
            </div>
        </div>
    </header>

    <div id="sticky-nav" class="sticky-nav">
        <div class="container mx-auto px-6 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <a href="#" class="logo-container text-xl font-bold text-transparent flex items-center">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="site-logo-sm">
                    <span class="animated-gradient-text">Aifrobeats</span>
                </a>
            </div>
            <!-- Desktop Navigation -->
            <div class="hidden md:flex space-x-4 items-center">
                <a href="#home" class="text-sm hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Home</a>
                <a href="#how-it-works" class="text-sm hover:text-green-600 transition-colors" style="color: var(--text-secondary);">How It Works</a>
                <a href="#examples" class="text-sm hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Our Music</a>
                <a href="music-library.html" class="text-sm hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Music Library</a>
                <a href="#pricing" class="text-sm hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Pricing</a>
                <a href="#order-form-section" class="primary-button text-sm py-2 px-4">Get Your Custom Song</a>
                <div class="theme-toggle ml-4" id="stickyThemeToggle" title="Toggle dark/light mode">
                    <i class="fas fa-moon"></i>
                </div>
            </div>
            <!-- Mobile Navigation -->
            <div class="md:hidden flex items-center justify-center ml-2">
                <a href="#order-form-section" class="primary-button text-xs py-1 px-2 mobile-get-song-btn">Get Song</a>
            </div>
        </div>
    </div>

    <section id="home" class="hero-bg text-white relative">
        <!-- Video Background -->
        <video class="hero-video" autoplay loop muted playsinline preload="auto" poster="images/background.jpg">
            <source src="video/bg.mp4" type="video/mp4">
            <!-- Fallback for browsers that don't support video -->
            Your browser does not support the video tag.
        </video>
        <div class="container mx-auto px-6 text-center relative z-10">
            <div data-aos="fade-up" data-aos-duration="1000">
                <span class="inline-block bg-green-500 text-white text-sm font-semibold px-4 py-1 rounded-full mb-6">CUSTOM MUSIC IN HOURS, NOT MONTHS</span>
                <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    Professional Afrobeats <span class="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-green-200">Without The Studio Costs</span>
                </h1>
                <p class="text-lg md:text-xl text-gray-300 mb-6 max-w-3xl mx-auto">
                    Why spend thousands on studio time, musicians, and equipment? Get custom, high-quality Afrobeats music for any occasion in just 1-3 hours at a fraction of traditional costs.
                </p>
                <div class="bg-white bg-opacity-10 p-4 rounded-lg mb-10 max-w-2xl mx-auto">
                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <h3 class="text-2xl font-bold text-green-400">1-3 Hours</h3>
                            <p class="text-sm">Delivery Time</p>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-green-400">$10</h3>
                            <p class="text-sm">Starting Price</p>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-green-400">100%</h3>
                            <p class="text-sm">Ownership Rights</p>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col sm:flex-row justify-center gap-4">
                    <a href="#order-form-section" class="primary-button text-lg flex items-center justify-center">
                        <i class="fas fa-music mr-2"></i>Get Your Custom Song Now
                    </a>
                    <a href="#examples" class="secondary-button bg-white bg-opacity-10 text-white border-white flex items-center justify-center">
                        <i class="fas fa-headphones mr-2"></i>Hear Examples
                    </a>
                </div>
            </div>
            <div class="mt-12 relative" data-aos="zoom-in" data-aos-delay="300">
                <div class="production-timeline rounded-xl shadow-2xl max-w-3xl w-full mx-auto overflow-hidden p-8 relative" style="background: var(--timeline-bg); opacity: var(--timeline-bg-opacity);">
                    <h3 class="text-white text-xl font-bold mb-8 text-center">From Your Idea to Professional Track in Hours</h3>

                    <!-- Timeline -->
                    <div class="relative overflow-hidden py-8">
                        <!-- Background Timeline line - centered in the container -->
                        <div class="absolute h-2 top-1/2 left-2 right-2 transform -translate-y-1/2 rounded-full" style="background-color: var(--timeline-line-bg);"></div>

                        <!-- Progress Timeline line (animated) - contained within left and right margins -->
                        <div id="timelineProgress" class="absolute h-2 top-1/2 left-2 transform -translate-y-1/2 rounded-full transition-all duration-1000 ease-in-out" style="background: var(--timeline-progress-bg); width: 0%; max-width: calc(100% - 16px);">
                            <!-- Handle is now positioned absolutely within the progress bar with no transforms that could affect layout -->
                            <div class="absolute right-0 top-1/2 -translate-y-1/2 w-4 h-4 bg-white rounded-full shadow-md" style="border: 2px solid var(--timeline-step-active-bg);"></div>
                        </div>

                        <!-- Timeline steps -->
                        <div class="relative flex justify-between items-start px-2 md:px-4">
                            <!-- Step 1 -->
                            <div class="timeline-step active flex flex-col items-center" data-step="1" role="tabpanel" aria-labelledby="timeline-step-1">
                                <div class="text-center w-full mb-4">
                                    <h4 class="font-semibold transition-all duration-300" style="color: var(--timeline-text-active);">Your Brief</h4>
                                </div>
                                <div class="w-12 h-12 md:w-14 md:h-14 rounded-full flex items-center justify-center text-white text-xl font-bold relative z-10 shadow-lg transform transition-all duration-500 ease-in-out mx-auto" id="timeline-step-1" role="tab" aria-selected="true" style="background-color: var(--timeline-step-active-bg);">1</div>
                                <div class="text-center w-full mt-4">
                                    <p class="text-white text-sm transition-all duration-300">Share your vision</p>
                                </div>
                            </div>

                            <!-- Step 2 -->
                            <div class="timeline-step flex flex-col items-center" data-step="2" role="tabpanel" aria-labelledby="timeline-step-2">
                                <div class="text-center w-full mb-4">
                                    <h4 class="font-semibold transition-all duration-300" style="color: var(--timeline-text-inactive);">AI Creation</h4>
                                </div>
                                <div class="w-12 h-12 md:w-14 md:h-14 rounded-full flex items-center justify-center text-white text-xl font-bold relative z-10 shadow-md transform transition-all duration-500 ease-in-out mx-auto" id="timeline-step-2" role="tab" aria-selected="false" style="background-color: var(--timeline-step-inactive-bg);">2</div>
                                <div class="text-center w-full mt-4">
                                    <p class="text-white text-sm transition-all duration-300">Generate options</p>
                                </div>
                            </div>

                            <!-- Step 3 -->
                            <div class="timeline-step flex flex-col items-center" data-step="3" role="tabpanel" aria-labelledby="timeline-step-3">
                                <div class="text-center w-full mb-4">
                                    <h4 class="font-semibold transition-all duration-300" style="color: var(--timeline-text-inactive);">Expert Curation</h4>
                                </div>
                                <div class="w-12 h-12 md:w-14 md:h-14 rounded-full flex items-center justify-center text-white text-xl font-bold relative z-10 shadow-md transform transition-all duration-500 ease-in-out mx-auto" id="timeline-step-3" role="tab" aria-selected="false" style="background-color: var(--timeline-step-inactive-bg);">3</div>
                                <div class="text-center w-full mt-4">
                                    <p class="text-white text-sm transition-all duration-300">Producer selection</p>
                                </div>
                            </div>

                            <!-- Step 4 -->
                            <div class="timeline-step flex flex-col items-center" data-step="4" role="tabpanel" aria-labelledby="timeline-step-4">
                                <div class="text-center w-full mb-4">
                                    <h4 class="font-semibold transition-all duration-300" style="color: var(--timeline-text-inactive);">Delivery</h4>
                                </div>
                                <div class="w-12 h-12 md:w-14 md:h-14 rounded-full flex items-center justify-center text-white text-xl font-bold relative z-10 shadow-md transform transition-all duration-500 ease-in-out mx-auto" id="timeline-step-4" role="tab" aria-selected="false" style="background-color: var(--timeline-step-inactive-bg);">4</div>
                                <div class="text-center w-full mt-4">
                                    <p class="text-white text-sm transition-all duration-300">Your custom track</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timeline content -->
                    <div class="mt-10 p-6 rounded-lg shadow-inner" style="background: var(--timeline-content-bg);">
                        <div id="timelineContent" class="text-white text-center opacity-100 transition-opacity duration-500 ease-in-out">
                            <h4 class="text-xl font-bold mb-3" style="color: var(--timeline-text-active);">Share Your Vision</h4>
                            <p>Tell us about your song idea, occasion, names, and the vibe you want. The more details you provide, the better your results!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="advantage" class="py-16 md:py-24">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16" data-aos="fade-up">
                <span class="inline-block bg-green-100 text-green-600 text-sm font-semibold px-4 py-1 rounded-full mb-4">SAVE TIME & MONEY</span>
                <h2 class="text-3xl md:text-4xl font-bold mb-4 theme-text">Why Choose Aifrobeats?</h2>
                <p class="text-xl theme-text-muted max-w-2xl mx-auto">Professional Music Production Without The Hassle</p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="card text-center" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 theme-text">Ready in Hours</h3>
                    <p class="theme-text-muted">Traditional studios take weeks or months. We deliver your custom music in just 1-3 hours after your order.</p>
                </div>
                <div class="card text-center" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 theme-text">Save Thousands</h3>
                    <p class="theme-text-muted">No need to hire musicians, rent studios, or buy equipment. Get professional quality music starting at just $10.</p>
                </div>
                <div class="card text-center" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-headphones"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 theme-text">Professional Quality</h3>
                    <p class="theme-text-muted">Our experienced producers ensure every track meets the highest standards before delivery to you.</p>
                </div>
                <div class="card text-center" data-aos="fade-up" data-aos-delay="400">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 theme-text">Perfectly Tailored</h3>
                    <p class="theme-text-muted">Your detailed input guides our creation process, ensuring the final track perfectly matches your vision and needs.</p>
                </div>
            </div>
        </div>
    </section>

    <section id="how-it-works" class="py-16 md:py-24 section-bg relative">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16" data-aos="fade-up">
                <span class="inline-block bg-green-100 text-green-600 text-sm font-semibold px-4 py-1 rounded-full mb-4">SIMPLE PROCESS</span>
                <h2 class="text-3xl md:text-4xl font-bold mb-4 theme-text">Custom Music In Just 3 Simple Steps</h2>
                <p class="text-xl theme-text-muted max-w-2xl mx-auto">From Your Idea To Finished Song In Hours</p>
            </div>
            <div class="grid md:grid-cols-3 gap-8 md:gap-12 items-stretch">
                <div class="card text-center relative flex flex-col h-full" data-aos="fade-up" data-aos-delay="100">
                    <div class="absolute -top-4 -left-4 w-12 h-12 bg-gradient-to-r from-green-600 to-green-400 rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">1</div>
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-edit"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 theme-text">Describe Your Vision</h3>
                    <p class="theme-text-muted">Tell us about your song idea, occasion, names, and the vibe you want. The more details you provide, the better your results!</p>
                </div>
                <div class="card text-center relative flex flex-col h-full" data-aos="fade-up" data-aos-delay="200">
                    <div class="absolute -top-4 -left-4 w-12 h-12 bg-gradient-to-r from-green-600 to-green-400 rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">2</div>
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 theme-text">We Create Your Music</h3>
                    <p class="theme-text-muted">Our advanced technology creates multiple options, then our professional producers select the perfect track that matches your brief.</p>
                </div>
                <div class="card text-center relative flex flex-col h-full" data-aos="fade-up" data-aos-delay="300">
                    <div class="absolute -top-4 -left-4 w-12 h-12 bg-gradient-to-r from-green-600 to-green-400 rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">3</div>
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 theme-text">Receive Your Custom Song</h3>
                    <p class="theme-text-muted">Get your professionally produced tracks via WhatsApp, Telegram, or Email within just 1-3 hours of ordering.</p>
                </div>
            </div>
            <div class="text-center mt-16" data-aos="fade-up" data-aos-delay="400">
                <a href="#order-form-section" class="primary-button text-lg inline-flex items-center">
                    <i class="fas fa-music mr-2"></i>Start Your Music Brief Now
                </a>
            </div>
        </div>
    </section>

    <section id="examples" class="py-16 md:py-24">
        <div class="container mx-auto px-6">
            <div class="text-center mb-8" data-aos="fade-up">
                <span class="inline-block bg-green-100 text-green-600 text-sm font-semibold px-4 py-1 rounded-full mb-4">HOT RELEASES</span>
                <h2 class="text-3xl md:text-4xl font-bold mb-4 theme-text">Check Out Our Recent Tracks</h2>
                <p class="text-xl theme-text-muted max-w-2xl mx-auto">Listen to what our customers are loving</p>
            </div>

            <div class="theme-bg p-6 rounded-xl mb-12 flex flex-col md:flex-row items-center justify-between" data-aos="fade-up">
                <div class="flex items-center mb-4 md:mb-0">
                    <div class="text-green-600 text-4xl mr-4">
                        <i class="fas fa-headphones-alt"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-xl theme-text">Click any track to listen instantly</h3>
                        <p class="theme-text-muted">Our custom music player lets you browse while listening</p>
                    </div>
                </div>
                <a href="#order-form-section" class="primary-button flex items-center">
                    <i class="fas fa-music mr-2"></i>Get Your Own Custom Track
                </a>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="card group" data-aos="fade-up" data-aos-delay="100">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <div class="absolute top-0 right-0 bg-green-600 text-white text-xs font-bold px-3 py-1 rounded-bl-lg z-10">NEW</div>
                        <img src="images/delia.png" alt="Delia's Song" class="h-48 w-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <span class="youtube-play-btn cursor-pointer play-example" data-track="0">
                                <div class="youtube-play-btn-inner">
                                    <div class="youtube-play-btn-triangle"></div>
                                </div>
                            </span>
                        </div>
                    </div>
                    <h3 class="text-xl font-semibold mb-1 theme-text">Delia's Song</h3>
                    <p class="text-sm theme-text-muted mb-2">Detergent Brand Jingle</p>
                    <p class="theme-text-muted text-sm mb-3">"An upbeat, danceable jingle created for Delia detergent brand with a catchy rhythm that sticks in your head."</p>
                    <button class="w-full py-2 px-4 bg-green-50 text-green-600 rounded-lg flex items-center justify-center hover:bg-green-100 transition-colors play-example" data-track="0">
                        <i class="fas fa-play mr-2"></i><span>Play in Music Player</span>
                    </button>
                </div>
                <div class="card group" data-aos="fade-up" data-aos-delay="200">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <div class="absolute top-0 right-0 bg-blue-600 text-white text-xs font-bold px-3 py-1 rounded-bl-lg z-10">POPULAR</div>
                        <img src="https://images.unsplash.com/photo-1655683576616-c40706fa79f2?ixlib=rb-4.1.0&auto=format&fit=crop&w=1950&q=80" alt="Valentine Song" class="h-48 w-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <span class="youtube-play-btn cursor-pointer play-example" data-track="1">
                                <div class="youtube-play-btn-inner">
                                    <div class="youtube-play-btn-triangle"></div>
                                </div>
                            </span>
                        </div>
                    </div>
                    <h3 class="text-xl font-semibold mb-1 theme-text">Milele Na Wewe</h3>
                    <p class="text-sm theme-text-muted mb-2">Romantic Valentine's Day song</p>
                    <p class="theme-text-muted text-sm mb-3">"This heartfelt love song was created for a man who wanted to express his deep love and commitment to his partner Stella. The title means 'Forever With You' in Swahili, symbolizing eternal love."</p>
                    <button class="w-full py-2 px-4 bg-green-50 text-green-600 rounded-lg flex items-center justify-center hover:bg-green-100 transition-colors play-example" data-track="1">
                        <i class="fas fa-play mr-2"></i><span>Play in Music Player</span>
                    </button>
                </div>
                <div class="card group" data-aos="fade-up" data-aos-delay="300">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <div class="absolute top-0 right-0 bg-yellow-500 text-white text-xs font-bold px-3 py-1 rounded-bl-lg z-10">TRENDING</div>
                        <img src="https://images.unsplash.com/photo-1609220136736-443140cffec6?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80" alt="Family Moment" class="h-48 w-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <span class="youtube-play-btn cursor-pointer play-example" data-track="2">
                                <div class="youtube-play-btn-inner">
                                    <div class="youtube-play-btn-triangle"></div>
                                </div>
                            </span>
                        </div>
                    </div>
                    <h3 class="text-xl font-semibold mb-1 theme-text">"Chica Benita" Family Moment</h3>
                    <p class="text-sm theme-text-muted mb-2">Heartfelt song for wife and children</p>
                    <p class="theme-text-muted text-sm mb-3">"This song was created for a husband to commemorate the beautiful feeling of coming home to see his kids playing with their mom Benita. The emotional melody captures that precious family moment that he wanted to preserve forever."</p>
                    <button class="w-full py-2 px-4 bg-green-50 text-green-600 rounded-lg flex items-center justify-center hover:bg-green-100 transition-colors play-example" data-track="2">
                        <i class="fas fa-play mr-2"></i><span>Play in Music Player</span>
                    </button>
                </div>
                <div class="card group" data-aos="fade-up" data-aos-delay="100">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <div class="absolute top-0 right-0 bg-pink-600 text-white text-xs font-bold px-3 py-1 rounded-bl-lg z-10">FEATURED</div>
                        <img src="https://images.unsplash.com/photo-1614291129408-3dd5436942e6?ixlib=rb-4.1.0&auto=format&fit=crop&w=1950&q=80" alt="Sarah's Song" class="h-48 w-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <span class="youtube-play-btn cursor-pointer play-example" data-track="3">
                                <div class="youtube-play-btn-inner">
                                    <div class="youtube-play-btn-triangle"></div>
                                </div>
                            </span>
                        </div>
                    </div>
                    <h3 class="text-xl font-semibold mb-1 theme-text">Sarah's Song (Version 1)</h3>
                    <p class="text-sm theme-text-muted mb-2">Celebration melody for a loving wife</p>
                    <p class="theme-text-muted text-sm mb-3">"A husband wanted to surprise his wife Sarah with a song that expressed his appreciation for her love and support. This celebratory melody combines traditional African rhythms with modern production techniques."</p>
                    <button class="w-full py-2 px-4 bg-green-50 text-green-600 rounded-lg flex items-center justify-center hover:bg-green-100 transition-colors play-example" data-track="3">
                        <i class="fas fa-play mr-2"></i><span>Play in Music Player</span>
                    </button>
                </div>
                <div class="card group" data-aos="fade-up" data-aos-delay="200">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <div class="absolute top-0 right-0 bg-purple-600 text-white text-xs font-bold px-3 py-1 rounded-bl-lg z-10">EXCLUSIVE</div>
                        <img src="https://images.unsplash.com/photo-1541532713592-79a0317b6b77?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80" alt="Last Man Standing" class="h-48 w-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <span class="youtube-play-btn cursor-pointer play-example" data-track="4">
                                <div class="youtube-play-btn-inner">
                                    <div class="youtube-play-btn-triangle"></div>
                                </div>
                            </span>
                        </div>
                    </div>
                    <h3 class="text-xl font-semibold mb-1 theme-text">Last Man Standing</h3>
                    <p class="text-sm theme-text-muted mb-2">Triumphant anthem of perseverance</p>
                    <p class="theme-text-muted text-sm mb-3">"This powerful track was created for a prestigious Lagos club, celebrating those who endure challenges and emerge successful. The dramatic production builds throughout with military-inspired percussion and victorious brass elements."</p>
                    <button class="w-full py-2 px-4 bg-green-50 text-green-600 rounded-lg flex items-center justify-center hover:bg-green-100 transition-colors play-example" data-track="4">
                        <i class="fas fa-play mr-2"></i><span>Play in Music Player</span>
                    </button>
                </div>
                <div class="card group" data-aos="fade-up" data-aos-delay="300">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <div class="absolute top-0 right-0 bg-red-600 text-white text-xs font-bold px-3 py-1 rounded-bl-lg z-10">HOT</div>
                        <img src="https://images.unsplash.com/photo-1581952976147-5a2d15560349?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80" alt="Hood Love" class="h-48 w-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <span class="youtube-play-btn cursor-pointer play-example" data-track="5">
                                <div class="youtube-play-btn-inner">
                                    <div class="youtube-play-btn-triangle"></div>
                                </div>
                            </span>
                        </div>
                    </div>
                    <h3 class="text-xl font-semibold mb-1 theme-text">Hood Love</h3>
                    <p class="text-sm theme-text-muted mb-2">Authentic street romance track</p>
                    <p class="theme-text-muted text-sm mb-3">"This authentic track tells a story of love that flourishes despite challenging circumstances. We balanced gritty urban production elements with softer melodic components to create contrast that enhances the storytelling."</p>
                    <button class="w-full py-2 px-4 bg-green-50 text-green-600 rounded-lg flex items-center justify-center hover:bg-green-100 transition-colors play-example" data-track="5">
                        <i class="fas fa-play mr-2"></i><span>Play in Music Player</span>
                    </button>
                </div>
            </div>
            <div class="card group" data-aos="fade-up" data-aos-delay="400">
                <div class="relative overflow-hidden rounded-lg mb-4">
                    <div class="absolute top-0 right-0 bg-green-600 text-white text-xs font-bold px-3 py-1 rounded-bl-lg z-10">NEW</div>
                    <img src="https://images.unsplash.com/photo-1686721454934-d874ad6e2ce7?ixlib=rb-4.1.0&auto=format&fit=crop&w=1950&q=80" alt="School Anthem" class="h-48 w-full object-cover transition-transform duration-500 group-hover:scale-110">
                    <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <span class="youtube-play-btn cursor-pointer play-example" data-track="6">
                            <div class="youtube-play-btn-inner">
                                <div class="youtube-play-btn-triangle"></div>
                            </div>
                        </span>
                    </div>
                </div>
                <h3 class="text-xl font-semibold mb-1 theme-text">Eagles for Glory</h3>
                <p class="text-sm theme-text-muted mb-2">School anthem with Fela-inspired Afrobeat</p>
                <p class="theme-text-muted text-sm mb-3">"This energetic anthem was commissioned by a school principal to introduce students to the rich heritage of Afrobeat music. The song captures the school's spirit while paying homage to Fela Kuti's influential style."</p>
                <button class="w-full py-2 px-4 bg-green-50 text-green-600 rounded-lg flex items-center justify-center hover:bg-green-100 transition-colors play-example" data-track="6">
                    <i class="fas fa-play mr-2"></i><span>Play in Music Player</span>
                </button>
            </div>
            <div class="text-center mt-12" data-aos="fade-up" data-aos-delay="400">
                <a href="music-library.html" class="secondary-button inline-flex items-center">
                    <i class="fas fa-headphones mr-2"></i>Browse Our Full Music Library
                </a>
            </div>
        </div>
    </section>

    <section id="use-cases" class="py-16 md:py-24 section-bg">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16" data-aos="fade-up">
                <span class="inline-block bg-green-100 text-green-600 text-sm font-semibold px-4 py-1 rounded-full mb-4">APPLICATIONS</span>
                <h2 class="text-3xl md:text-4xl font-bold mb-4 theme-text">Perfect for Any Vibe, Any Occasion</h2>
                <p class="text-xl theme-text-muted max-w-2xl mx-auto">What Song do you want us to create?</p>
            </div>
            <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-6">
                <a href="#order-form-section" class="use-case-item p-6 theme-bg rounded-xl shadow-md hover:shadow-xl transition-all block cursor-pointer" data-aos="fade-up" data-aos-delay="100" onclick="selectSongType('wedding-bride')">
                    <div class="feature-icon mx-auto text-2xl w-16 h-16 mb-4">
                        <i class="fas fa-ring"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 theme-text text-center">Weddings</h3>
                    <p class="text-sm theme-text-muted text-center">Celebrate your special day with custom Afrobeats</p>
                </a>
                <a href="#order-form-section" class="use-case-item p-6 theme-bg rounded-xl shadow-md hover:shadow-xl transition-all block cursor-pointer" data-aos="fade-up" data-aos-delay="150" onclick="selectSongType('birthday-friend')">
                    <div class="feature-icon mx-auto text-2xl w-16 h-16 mb-4">
                        <i class="fas fa-birthday-cake"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 theme-text text-center">Birthdays</h3>
                    <p class="text-sm theme-text-muted text-center">Make someone's special day even more memorable</p>
                </a>
                <a href="#order-form-section" class="use-case-item p-6 theme-bg rounded-xl shadow-md hover:shadow-xl transition-all block cursor-pointer" data-aos="fade-up" data-aos-delay="200" onclick="selectSongType('jingle')">
                    <div class="feature-icon mx-auto text-2xl w-16 h-16 mb-4">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 theme-text text-center">Business Jingles</h3>
                    <p class="text-sm theme-text-muted text-center">Catchy tunes to promote your brand and products</p>
                </a>
                <a href="#order-form-section" class="use-case-item p-6 theme-bg rounded-xl shadow-md hover:shadow-xl transition-all block cursor-pointer" data-aos="fade-up" data-aos-delay="250" onclick="selectSongType('coach')">
                    <div class="feature-icon mx-auto text-2xl w-16 h-16 mb-4">
                        <i class="fas fa-dumbbell"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 theme-text text-center">Fitness</h3>
                    <p class="text-sm theme-text-muted text-center">Energizing beats to power your workout sessions</p>
                </a>
                <a href="#order-form-section" class="use-case-item p-6 theme-bg rounded-xl shadow-md hover:shadow-xl transition-all block cursor-pointer" data-aos="fade-up" data-aos-delay="300" onclick="selectSongType('corporate-event')">
                    <div class="feature-icon mx-auto text-2xl w-16 h-16 mb-4">
                        <i class="fas fa-building"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 theme-text text-center">Corporate</h3>
                    <p class="text-sm theme-text-muted text-center">Professional tracks for events and presentations</p>
                </a>
                <a href="#order-form-section" class="use-case-item p-6 theme-bg rounded-xl shadow-md hover:shadow-xl transition-all block cursor-pointer" data-aos="fade-up" data-aos-delay="350" onclick="selectSongType('love-song')">
                    <div class="feature-icon mx-auto text-2xl w-16 h-16 mb-4">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 theme-text text-center">Love Songs</h3>
                    <p class="text-sm theme-text-muted text-center">Express your feelings with a personalized melody</p>
                </a>
                <a href="#order-form-section" class="use-case-item p-6 theme-bg rounded-xl shadow-md hover:shadow-xl transition-all block cursor-pointer" data-aos="fade-up" data-aos-delay="400" onclick="selectSongType('album-release')">
                    <div class="feature-icon mx-auto text-2xl w-16 h-16 mb-4">
                        <i class="fas fa-film"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 theme-text text-center">Content Creators</h3>
                    <p class="text-sm theme-text-muted text-center">Stand out with unique music for your videos</p>
                </a>
                <a href="#order-form-section" class="use-case-item p-6 theme-bg rounded-xl shadow-md hover:shadow-xl transition-all block cursor-pointer" data-aos="fade-up" data-aos-delay="450" onclick="selectSongType('donation')">
                    <div class="feature-icon mx-auto text-2xl w-16 h-16 mb-4">
                        <i class="fas fa-hand-holding-heart"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 theme-text text-center">Charity</h3>
                    <p class="text-sm theme-text-muted text-center">Inspiring tracks for fundraisers and campaigns</p>
                </a>
                <a href="#order-form-section" class="use-case-item p-6 theme-bg rounded-xl shadow-md hover:shadow-xl transition-all block cursor-pointer" data-aos="fade-up" data-aos-delay="500" onclick="selectSongType('club')">
                    <div class="feature-icon mx-auto text-2xl w-16 h-16 mb-4">
                        <i class="fas fa-music"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 theme-text text-center">Club Anthems</h3>
                    <p class="text-sm theme-text-muted text-center">Get the dance floor moving with vibrant beats</p>
                </a>
                <a href="#order-form-section" class="use-case-item p-6 theme-bg rounded-xl shadow-md hover:shadow-xl transition-all block cursor-pointer" data-aos="fade-up" data-aos-delay="550" onclick="selectSongType('custom')">
                    <div class="feature-icon mx-auto text-2xl w-16 h-16 mb-4">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 theme-text text-center">Custom</h3>
                    <p class="text-sm theme-text-muted text-center">Tell us your unique idea and we'll bring it to life</p>
                </a>
            </div>
        </div>
    </section>

    <section id="pricing" class="py-16 md:py-24">
        <div class="container mx-auto px-6 text-center">
            <div data-aos="fade-up">
                <span class="inline-block bg-green-100 text-green-600 text-sm font-semibold px-4 py-1 rounded-full mb-4">PRICING</span>
                <h2 class="text-3xl md:text-4xl font-bold mb-4 theme-text">Ready for Your Unique Aifrobeats?</h2>
                <p class="text-xl theme-text-muted mb-10 max-w-2xl mx-auto">Get AI-generated Afrobeats songs, carefully selected by our producers to match your vision!</p>
            </div>

            <div class="theme-bg p-8 md:p-12 rounded-2xl shadow-xl max-w-lg mx-auto border-2 border-green-500 relative overflow-hidden" data-aos="zoom-in" data-aos-delay="200">
                <div class="absolute -top-12 -right-12 w-24 h-24 bg-green-500 rotate-45 z-0"></div>
                <div class="absolute top-0 right-0 bg-green-600 text-white py-1 px-4 text-sm font-bold z-10">BEST VALUE</div>
                <div class="relative z-10">
                    <h3 class="text-2xl font-bold text-green-600 mb-2">Special Offer!</h3>
                    <div class="flex items-baseline justify-center">
                        <span class="text-4xl font-bold">$10</span>
                        <span class="text-gray-500 ml-1 line-through">$50</span>
                    </div>
                    <p class="theme-text mb-6 font-semibold">For 2 Vetted High Quality Songs + 1 Bonus Track!</p>
                    <ul class="text-left space-y-3 theme-text-muted mb-8">
                        <li class="flex items-start">
                            <span class="text-green-500 mr-2 mt-1"><i class="fas fa-check-circle"></i></span>
                            <span>AI-Generated, Producer-Selected</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-500 mr-2 mt-1"><i class="fas fa-check-circle"></i></span>
                            <span>Full Ownership & Commercial Rights</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-500 mr-2 mt-1"><i class="fas fa-check-circle"></i></span>
                            <span>Delivery: WhatsApp, Telegram, Email</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-500 mr-2 mt-1"><i class="fas fa-check-circle"></i></span>
                            <span>Turnaround: 1-3 Hours</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-500 mr-2 mt-1"><i class="fas fa-check-circle"></i></span>
                            <span>Revisions: Up to 2 re-selections by our producer from the AI-generated pool if the first isn't quite right.</span>
                        </li>
                    </ul>
                    <a href="#order-form-section" class="primary-button w-full text-lg flex items-center justify-center">
                        <i class="fas fa-music mr-2"></i>Order Your AI Afrobeats Now
                    </a>
                    <div class="flex items-center justify-center mt-6 text-gray-500">
                        <i class="fas fa-shield-alt mr-2"></i>
                        <p class="text-sm">
                            Secure payment & <a href="terms.html" class="underline hover:text-green-600">100% satisfaction guarantee</a>
                        </p>
                    </div>
                </div>
            </div>

            <div class="mt-8 theme-text-muted text-sm max-w-lg mx-auto" data-aos="fade-up" data-aos-delay="300">
                By ordering, you agree to our <a href="terms.html" class="underline hover:text-green-600">Terms of Service</a> and <a href="privacy.html" class="underline hover:text-green-600">Privacy Policy</a>.
            </div>
        </div>
    </section>

    <section id="testimonials" class="py-16 md:py-24">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16" data-aos="fade-up">
                <span class="inline-block bg-green-100 text-green-600 text-sm font-semibold px-4 py-1 rounded-full mb-4">TESTIMONIALS</span>
                <h2 class="text-3xl md:text-4xl font-bold mb-4 theme-text">What Our Customers Are Saying</h2>
                <p class="text-xl theme-text-muted max-w-2xl mx-auto">Real feedback from satisfied clients</p>
            </div>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="card relative" data-aos="fade-up" data-aos-delay="100">
                    <div class="absolute -top-5 -left-5 w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <p class="theme-text-muted italic mb-6">"I needed a unique track for my fitness studio's promotional videos. Aifrobeats delivered an energetic, motivating piece that perfectly captures our brand vibe. My clients love it, and it cost a fraction of what local studios quoted me!"</p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gray-200 rounded-full overflow-hidden mr-4">
                            <img src="https://images.unsplash.com/photo-1589860518300-9eac95f784d9?ixlib=rb-4.1.0&auto=format&fit=crop&w=800&q=80" alt="Jennifer R." class="w-full h-full object-cover">
                        </div>
                        <div>
                            <p class="font-semibold theme-text">Jennifer R.</p>
                            <p class="text-sm theme-text-muted">Fitness Studio Owner, Los Angeles</p>
                        </div>
                    </div>
                </div>
                <div class="card relative" data-aos="fade-up" data-aos-delay="200">
                    <div class="absolute -top-5 -left-5 w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <p class="theme-text-muted italic mb-6">"The wedding song Aifrobeats created for us blended traditional African rhythms with modern sounds perfectly. Our guests were amazed it was custom-made in just hours. It's now 'our song' and brings back beautiful memories every time we hear it."</p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gray-200 rounded-full overflow-hidden mr-4">
                            <img src="https://images.unsplash.com/photo-1522529599102-193c0d76b5b6?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="Thabo M." class="w-full h-full object-cover">
                        </div>
                        <div>
                            <p class="font-semibold theme-text">Thabo M.</p>
                            <p class="text-sm theme-text-muted">Software Engineer, Cape Town</p>
                        </div>
                    </div>
                </div>
                <div class="card relative" data-aos="fade-up" data-aos-delay="300">
                    <div class="absolute -top-5 -left-5 w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <p class="theme-text-muted italic mb-6">"As a content creator, I needed unique background music that wouldn't get flagged for copyright. Aifrobeats delivered a catchy, professional track that has become my channel's signature sound. My viewers constantly ask where they can find it!"</p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gray-200 rounded-full overflow-hidden mr-4">
                            <img src="https://images.unsplash.com/photo-1600481176431-47ad2ab2745d?ixlib=rb-4.1.0&auto=format&fit=crop&w=800&q=80" alt="Min-Ji K." class="w-full h-full object-cover">
                        </div>
                        <div>
                            <p class="font-semibold theme-text">Min-Ji K.</p>
                            <p class="text-sm theme-text-muted">Content Creator, Seoul</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="order-form-section" class="py-16 md:py-24 section-bg">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12" data-aos="fade-up">
                <span class="inline-block bg-green-100 text-green-600 text-sm font-semibold px-4 py-1 rounded-full mb-4">ORDER NOW</span>
                <h2 class="text-3xl md:text-4xl font-bold mb-4 theme-text">Tell Us About Your Song Idea</h2>
                <p class="text-xl theme-text-muted max-w-2xl mx-auto">The more details you provide, the better our AI and producers can match your vision.</p>
            </div>

            <form id="orderForm" class="max-w-2xl mx-auto theme-bg p-8 rounded-2xl shadow-xl" data-aos="fade-up" data-aos-delay="200">
                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="name" class="block text-sm font-medium theme-text mb-1">Your Name *</label>
                        <input type="text" id="name" name="name" required class="form-input w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none">
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium theme-text mb-1">Email *</label>
                        <input type="email" id="email" name="email" required class="form-input w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none">
                    </div>
                </div>
                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="whatsapp" class="block text-sm font-medium theme-text mb-1">WhatsApp (for delivery)</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <i class="fab fa-whatsapp text-green-500"></i>
                            </div>
                            <input type="tel" id="whatsapp" name="whatsapp" class="form-input w-full pl-10 px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none">
                        </div>
                    </div>
                    <div>
                        <label for="telegram" class="block text-sm font-medium theme-text mb-1">Telegram (for delivery)</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <i class="fab fa-telegram-plane text-blue-500"></i>
                            </div>
                            <input type="text" id="telegram" name="telegram" class="form-input w-full pl-10 px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none">
                        </div>
                    </div>
                </div>
                <div class="mb-6">
                    <label for="song-type" class="block text-sm font-medium theme-text mb-1">Select Your Song Type *</label>
                    <select id="song-type" name="song-type" required class="form-input w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none">
                        <option value="">--Please choose an option--</option>
                        <option value="jingle">Jingle for My Business</option>
                        <option value="wedding-bride">Wedding day song for the bride</option>
                        <option value="graduation-child">Graduation song for a son or daughter</option>
                        <option value="first-steps">First steps song for a baby</option>
                        <option value="anniversary">Anniversary song for a couple</option>
                        <option value="birthday-friend">Birthday song for a best friend</option>
                        <option value="retirement">Retirement song for a colleague</option>
                        <option value="engagement">Engagement song for a newly engaged couple</option>
                        <option value="pregnancy">Pregnancy announcement song for expecting parents</option>
                        <option value="first-date">First date song for a new couple</option>
                        <option value="farewell">Farewell song for a friend moving away</option>
                        <option value="reunion">Reunion song for long-lost friends</option>
                        <option value="proposal">Wedding proposal song</option>
                        <option value="first-home">First home purchase song</option>
                        <option value="new-job">New job celebration song</option>
                        <option value="recovery">Recovery song for someone overcoming an illness</option>
                        <option value="memorial">Memorial song for a loved one who has passed away</option>
                        <option value="coming-of-age">Coming of age song for a teenager</option>
                        <option value="high-school">Graduation from high school song</option>
                        <option value="graduate-school">Graduation from graduate school song</option>
                        <option value="first-car">First car purchase song</option>
                        <option value="first-paycheck">First paycheck song</option>
                        <option value="promotion">Promotion song for a hardworking employee</option>
                        <option value="first-school">Song for a child's first day of school</option>
                        <option value="lost-tooth">Song for a child's first lost tooth</option>
                        <option value="pet-birthday">Song for a pet's birthday</option>
                        <option value="team-victory">Song for a team's victory celebration</option>
                        <option value="millionaire">Song for A Millionaire's birthday</option>
                        <option value="billionaire">Song for A Billionaire's birthday</option>
                        <option value="company-anniversary">Song for a company's founding anniversary</option>
                        <option value="product-launch">Song for a company's successful product launch</option>
                        <option value="fundraising">Song for a non-profit's fundraising event</option>
                        <option value="festival">Song for a community's festival or fair</option>
                        <option value="centennial">Song for a city's centennial celebration</option>
                        <option value="independence">Song for a nation's independence day</option>
                        <option value="homecoming">Song for a soldier's homecoming</option>
                        <option value="nurse">Song for a nurse's appreciation day</option>
                        <option value="teacher">Song for a teacher's retirement</option>
                        <option value="police">Song for a police officer's service</option>
                        <option value="book-launch">Song for a writer's book launch</option>
                        <option value="album-release">Song for a musician's album release</option>
                        <option value="restaurant">Song for a chef's restaurant opening</option>
                        <option value="startup">Song for a entrepreneur's successful startup</option>
                        <option value="survivor">Song for a survivor's triumph over adversity</option>
                        <option value="travel">Song for a traveler's journey to a new country</option>
                        <option value="sobriety">Song for a recovering addict's sobriety milestone</option>
                        <option value="campaign">Song for an activist's successful campaign</option>
                        <option value="mentor">Song for a mentor's guidance and wisdom</option>
                        <option value="donation">Song for a philanthropist's generous donation</option>
                        <option value="coach">Song for a coach's motivation and inspiration</option>
                        <option value="athlete">Song for an athlete's sportsmanship and teamwork</option>
                        <option value="fan">Song for a fan's loyalty and enthusiasm</option>
                        <option value="academic">Song for a student's academic achievement</option>
                        <option value="breakup">Music for Recovering from a Breakup</option>
                        <option value="mindfulness">Mindfulness Music for Children</option>
                        <option value="other">Other (Describe Below)</option>
                    </select>
                    <p class="text-xs theme-text-muted mt-1">We can create custom music for virtually any occasion!</p>
                </div>
                <div class="mb-8">
                    <label for="song-idea" class="block text-sm font-medium theme-text mb-1">Describe Your Song Idea(s) *</label>
                    <textarea id="song-idea" name="song-idea" rows="6" required class="form-input w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none" placeholder="Explain what your song should be about, target names, mood, tempo, any specific lyrics or themes... The more details, the better our AI can generate and our producers can select!"></textarea>
                    <p class="text-xs theme-text-muted mt-1">Minimum 50 characters for best results.</p>
                </div>
                <div class="text-center">
                    <button type="submit" class="primary-button text-lg w-full md:w-auto px-8 py-3 flex items-center justify-center mx-auto">
                        <i class="fas fa-shopping-cart mr-2"></i>Submit & Proceed to Payment
                    </button>
                </div>
                <div class="flex items-center justify-center mt-6 theme-text-muted">
                    <i class="fas fa-lock mr-2"></i>
                    <p class="text-sm">Your information is secure and will never be shared.</p>
                </div>
            </form>
        </div>
    </section>

    <section id="faq" class="py-16 md:py-24 section-bg">
        <div class="container mx-auto px-6 max-w-4xl">
            <div class="text-center mb-16" data-aos="fade-up">
                <span class="inline-block bg-green-100 text-green-600 text-sm font-semibold px-4 py-1 rounded-full mb-4">FAQ</span>
                <h2 class="text-3xl md:text-4xl font-bold mb-4 theme-text">Frequently Asked Questions</h2>
                <p class="text-xl theme-text-muted max-w-2xl mx-auto">Everything you need to know about our AI-generated, human-curated Afrobeats.</p>
            </div>
            <div class="space-y-4" data-aos="fade-up" data-aos-delay="200">
                <div class="faq-item">
                    <div class="faq-header">
                        <span>How does the AI and human curation work?</span>
                        <i class="fas fa-chevron-down faq-icon text-green-500"></i>
                    </div>
                    <div class="faq-content theme-bg">
                        <p class="theme-text-muted">You provide a detailed brief. Our AI generates multiple Afrobeats tracks based on your input. Then, our experienced Afrobeats producers listen to these options and select the best one(s) that match your requirements and meet our quality standards.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-header">
                        <span>What if I don't like the first song selected?</span>
                        <i class="fas fa-chevron-down faq-icon text-green-500"></i>
                    </div>
                    <div class="faq-content theme-bg">
                        <p class="theme-text-muted">We want you to be happy! Our $10 package includes up to 2 re-selections. If the first track selected by our producer isn't quite right, they will go back to the pool of AI-generated options (from your original brief) and select an alternative for you. This can be done twice.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-header">
                        <span>How long does it take to get my music?</span>
                        <i class="fas fa-chevron-down faq-icon text-green-500"></i>
                    </div>
                    <div class="faq-content theme-bg">
                        <p class="theme-text-muted">Delivery is typically within 1 to 3 hours after you submit your brief and complete payment. We work quickly to generate options and have our producers select the best match for your needs.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-header">
                        <span>Do I own the rights to the music?</span>
                        <i class="fas fa-chevron-down faq-icon text-green-500"></i>
                    </div>
                    <div class="faq-content theme-bg">
                        <p class="theme-text-muted">Yes! You get full ownership and exclusive commercial rights to the music you receive. You can use it for personal or business purposes without any additional fees or royalties.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-header">
                        <span>What payment methods do you accept?</span>
                        <i class="fas fa-chevron-down faq-icon text-green-500"></i>
                    </div>
                    <div class="faq-content theme-bg">
                        <p class="theme-text-muted">We accept all major credit cards, PayPal, and mobile money options. Our payment process is secure and encrypted to protect your information.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
<section id="pidgin-chat" class="py-16 md:py-24 relative overflow-hidden">
  <!-- Fixed background image -->
  <div class="fixed-bg absolute inset-0 z-0" style="background-image: url('images/background.jpg');"></div>
  <div class="overlay absolute inset-0 bg-black bg-opacity-60 z-0"></div>
  
  <div class="container mx-auto px-6 text-center relative z-10">
    <h2 class="text-3xl md:text-4xl font-bold mb-6 text-white">Create Your Afrobeat Lyrics with Pidgin Chat</h2>
    <div class="max-w-xs mx-auto">
      <a href="https://aifrobeats.com/pidginchat" class="primary-button text-lg flex items-center justify-center">
        <i class="fas fa-paper-plane mr-2"></i> Start Chatting
      </a>
    </div>
  </div>
</section>

    <!-- Standard Footer - Can be reused across all pages -->
    <footer id="contact" class="site-footer bg-gray-900 text-gray-400 py-12 md:py-16">
        <div class="container mx-auto px-4 md:px-6 footer-container">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-10">
                <!-- Company Info -->
                <div class="footer-col">
                    <h3 class="text-xl md:text-2xl font-semibold text-white mb-4 flex items-center">
                        <img src="images/mlogo.png" alt="Aifrobeats Logo" class="w-8 h-8 mr-2">
                        <span class="animated-gradient-text">Aifrobeats</span>
                    </h3>
                    <p class="mb-4 text-sm md:text-base">AI-Generated Afrobeats, Human-Curated for you. Fast, affordable, and unique music for every occasion.</p>
                    <div class="flex items-center mb-3">
                        <i class="fas fa-envelope text-green-500 mr-3"></i>
                        <a href="mailto:<EMAIL>" class="hover:text-green-400 transition-colors"><EMAIL></a>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-phone-alt text-green-500 mr-3"></i>
                        <a href="tel:+2347038808350" class="hover:text-green-400 transition-colors">+234 ************</a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="footer-col">
                    <h3 class="text-lg md:text-xl font-semibold text-white mb-4">Quick Links</h3>
                    <ul class="space-y-2 footer-links">
                        <li>
                            <a href="index.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Home
                            </a>
                        </li>
                        <li>
                            <a href="index.html#how-it-works" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>How It Works
                            </a>
                        </li>
                        <li>
                            <a href="music-library.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Music Library
                            </a>
                        </li>
                        <li>
                            <a href="index.html#pricing" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Pricing
                            </a>
                        </li>
                        <li>
                            <a href="index.html#faq" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>FAQ
                            </a>
                        </li>
                        <li>
                            <a href="index.html#order-form-section" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Order Now
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Connect With Us -->
                <div class="footer-col">
                    <h3 class="text-lg md:text-xl font-semibold text-white mb-4">Connect With Us</h3>
                    <p class="mb-3 text-sm md:text-base">Follow us for updates and examples!</p>
                    <div class="flex space-x-3 mb-5">
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-soundcloud"></i>
                        </a>
                    </div>
                    <div class="bg-gray-800 p-3 rounded-lg newsletter-form">
                        <h4 class="text-white text-sm font-semibold mb-2">Subscribe to our newsletter</h4>
                        <form class="flex">
                            <input type="email" placeholder="Your email" class="form-input bg-gray-700 border-0 text-white text-sm rounded-l-lg focus:ring-green-500 focus:border-green-500 flex-grow">
                            <button type="submit" class="bg-green-600 text-white px-3 rounded-r-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Copyright -->
            <div class="mt-6 md:mt-8 pt-5 md:pt-6 border-t border-gray-700 text-center text-xs md:text-sm">
                <p>&copy; <span id="currentYear"></span> AIFROBEATS CUSTOM MUSIC PRODUCTIONS – All Rights Reserved.</p>
                <div class="mt-2 flex flex-wrap justify-center gap-4">
                    <a href="terms.html" class="hover:text-green-400 transition-colors">Terms of Service</a>
                    <span class="hidden md:inline">|</span>
                    <a href="privacy.html" class="hover:text-green-400 transition-colors">Privacy Policy</a>
                </div>
            </div>
        </div>
    </footer>



    <!-- Music Player Toggle Button -->
    <div class="music-player-toggle" id="musicPlayerToggle">
        <i class="fas fa-music"></i>
    </div>

    <!-- Fixed Music Player -->
    <div class="music-player-container active" id="musicPlayerContainer">
        <div class="music-player">
            <!-- Album Art with Vinyl Turntable -->
            <div class="music-player-album vinyl-container">
                <img id="playerAlbumArt" src="https://images.unsplash.com/photo-1511275539165-cc46b1ee89bf?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80" alt="Album Art">
                <img src="https://pngimg.com/uploads/vinyl/vinyl_PNG21.png" id="vynl-id" class="vinyl-record" alt="Vinyl Record">
            </div>

            <!-- Track Info -->
            <div class="music-player-info">
                <p class="music-player-title" id="playerTitle">Delia's Melody</p>
                <p class="music-player-artist" id="playerArtist">Custom Song (2023)</p>
            </div>

            <!-- Controls -->
            <div class="music-player-controls">
                <button class="music-player-control" id="prevButton">
                    <i class="fas fa-step-backward"></i>
                </button>
                <button class="music-player-control play-pause" id="playPauseButton">
                    <i class="fas fa-play" id="playPauseIcon"></i>
                </button>
                <button class="music-player-control" id="nextButton">
                    <i class="fas fa-step-forward"></i>
                </button>
            </div>

            <!-- Progress Bar -->
            <div class="music-player-progress-container" id="progressContainer">
                <div class="music-player-progress" id="progressBar">
                    <div class="music-player-progress-handle"></div>
                </div>
            </div>

            <!-- Time -->
            <div class="music-player-time">
                <span id="currentTime">0:00</span> / <span id="totalTime">0:00</span>
            </div>

            <!-- Volume -->
            <div class="music-player-volume-container">
                <div class="music-player-volume-icon" id="volumeIcon">
                    <i class="fas fa-volume-up"></i>
                </div>
                <div class="music-player-volume-slider" id="volumeSlider">
                    <div class="music-player-volume-level" id="volumeLevel"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden Audio Element -->
    <audio id="audioPlayer" preload="auto"></audio>





    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white p-6 rounded-lg shadow-xl text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500 mx-auto mb-4"></div>
            <p class="text-lg font-semibold">Sending your information...</p>
            <p class="text-sm text-gray-600 mt-2">You'll be redirected to complete your order shortly.</p>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>

    <!-- Firebase Auth State Management -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "YOUR_API_KEY_HERE",
            authDomain: "YOUR_PROJECT_ID.firebaseapp.com",
            projectId: "YOUR_PROJECT_ID",
            storageBucket: "YOUR_PROJECT_ID.appspot.com",
            messagingSenderId: "YOUR_SENDER_ID",
            appId: "YOUR_APP_ID"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        // Auth state management for navigation
        auth.onAuthStateChanged(async (user) => {
            const authButtons = document.getElementById('auth-buttons');
            const userMenu = document.getElementById('user-menu');
            const mobileAuthButtons = document.getElementById('mobile-auth-buttons');
            const mobileUserMenu = document.getElementById('mobile-user-menu');

            if (user) {
                // User is signed in - show user menu, hide auth buttons
                authButtons.classList.add('hidden');
                userMenu.classList.remove('hidden');
                mobileAuthButtons.classList.add('hidden');
                mobileUserMenu.classList.remove('hidden');

                // Load user credits
                try {
                    const userDoc = await db.collection('users').doc(user.uid).get();
                    if (userDoc.exists) {
                        const userData = userDoc.data();
                        const credits = userData.credits || 0;
                        document.getElementById('nav-user-credits').textContent = credits;
                        document.getElementById('mobile-nav-user-credits').textContent = credits;
                    }
                } catch (error) {
                    console.error('Error loading user credits:', error);
                }
            } else {
                // User is not signed in - show auth buttons, hide user menu
                authButtons.classList.remove('hidden');
                userMenu.classList.add('hidden');
                mobileAuthButtons.classList.remove('hidden');
                mobileUserMenu.classList.add('hidden');
            }
        });

        // Logout functionality
        document.getElementById('nav-logout-btn').addEventListener('click', async () => {
            try {
                await auth.signOut();
            } catch (error) {
                console.error('Error signing out:', error);
            }
        });

        document.getElementById('mobile-nav-logout-btn').addEventListener('click', async () => {
            try {
                await auth.signOut();
            } catch (error) {
                console.error('Error signing out:', error);
            }
        });
    </script>

    <!-- External JavaScript files -->
    <script src="main.js"></script>
    <script src="player.js"></script>
</body>
</html>

<!-- Pidgin Chat Section Template -->
<template id="pidgin-chat-template">
  <section id="pidgin-chat" class="py-16 md:py-24 relative overflow-hidden">
    <div class="fixed-bg absolute inset-0 z-0" style="background-image: url('images/background.jpg');"></div>
    <div class="overlay absolute inset-0 bg-black bg-opacity-60 z-0"></div>
    
    <div class="container mx-auto px-6 text-center relative z-10">
      <h2 class="text-3xl md:text-4xl font-bold mb-6 text-white">Create Your Afrobeat Lyrics with Pidgin Chat</h2>
      <div class="max-w-xs mx-auto">
        <a href="https://aifrobeats.com/pidginchat" class="primary-button text-lg flex items-center justify-center">
          <i class="fas fa-paper-plane mr-2"></i> Start Chatting
        </a>
      </div>
    </div>
  </section>
</template>
