<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Aifrobeats</title>
    <link rel="icon" href="images/mlogo.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    
    <!-- Firebase UI Auth CSS -->
    <link type="text/css" rel="stylesheet" href="https://www.gstatic.com/firebasejs/ui/6.0.1/firebase-ui-auth.css" />

    <style>
        /* Enhanced Firebase UI Styling */
        .firebase-ui-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Custom Firebase UI button styling */
        .firebase-ui-container .firebaseui-idp-button {
            background: linear-gradient(135deg, #10b981, #059669) !important;
            border: none !important;
            border-radius: 0.75rem !important;
            padding: 1rem !important;
            font-weight: 600 !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3) !important;
        }

        .firebase-ui-container .firebaseui-idp-button:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4) !important;
        }

        .firebase-ui-container .firebaseui-textfield {
            background: rgba(255, 255, 255, 0.15) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: 0.75rem !important;
            color: white !important;
            padding: 1rem !important;
        }

        .firebase-ui-container .firebaseui-textfield:focus {
            border-color: #10b981 !important;
            box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3) !important;
        }

        .firebase-ui-container .firebaseui-label {
            color: #e5e7eb !important;
            font-weight: 600 !important;
        }

        /* Enhanced form animations */
        .signup-form-container {
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Floating label effect */
        .form-group {
            position: relative;
        }

        .form-group input:focus + label,
        .form-group input:not(:placeholder-shown) + label {
            transform: translateY(-1.5rem) scale(0.8);
            color: #10b981;
        }

        .form-group label {
            position: absolute;
            left: 1rem;
            top: 1rem;
            transition: all 0.3s ease;
            pointer-events: none;
        }
    </style>
</head>
<body class="min-h-screen flex flex-col" style="background: linear-gradient(135deg, var(--hero-gradient-start), var(--hero-gradient-end));">

    <!-- Navigation Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-white bg-opacity-10 backdrop-blur-md">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.html" class="logo-container text-2xl font-bold text-transparent flex items-center">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="site-logo">
                    <span class="animated-gradient-text">Aifrobeats</span>
                </a>
            </div>
            <div class="flex items-center space-x-4">
                <a href="index.html" class="text-white hover:text-green-400 transition-colors">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
                <a href="login.html" class="secondary-button bg-white bg-opacity-10 text-white border-white">
                    <i class="fas fa-sign-in-alt mr-2"></i>Sign In
                </a>
            </div>
        </nav>
    </header>

    <!-- Main Content Area -->
    <main class="flex-1 flex items-center justify-center py-20">
        <!-- Signup Container -->
        <div class="w-full max-w-lg mx-auto px-6">
        <div class="signup-form-container bg-white bg-opacity-15 backdrop-blur-xl rounded-3xl shadow-2xl border border-white border-opacity-30 overflow-hidden">

            <!-- Header with Enhanced Design -->
            <div class="text-center pt-8 pb-6 px-8">
                <div class="relative mb-6">
                    <div class="w-24 h-24 mx-auto bg-gradient-to-br from-green-400 via-green-500 to-green-600 rounded-full flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-300">
                        <i class="fas fa-user-plus text-white text-3xl"></i>
                    </div>
                    <div class="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center shadow-md">
                        <span class="text-yellow-800 text-xs font-bold">3</span>
                    </div>
                </div>
                <h1 class="text-4xl font-bold text-white mb-3 bg-gradient-to-r from-white to-green-200 bg-clip-text text-transparent">Join Aifrobeats!</h1>
                <p class="text-gray-200 text-lg">Create your account and get 3 free preview credits</p>
            </div>

            <!-- Enhanced Welcome Offer -->
            <div class="mx-8 mb-8">
                <div class="bg-gradient-to-r from-green-500 via-green-600 to-emerald-600 rounded-2xl p-6 text-center shadow-xl border border-green-400 border-opacity-30">
                    <div class="flex items-center justify-center mb-3">
                        <div class="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-gift text-yellow-800 text-sm"></i>
                        </div>
                        <h3 class="text-white font-bold text-xl">Welcome Bonus!</h3>
                    </div>
                    <p class="text-green-100 text-base mb-4 font-medium">Get 3 FREE preview credits when you sign up</p>
                    <div class="grid grid-cols-2 gap-3 text-sm text-green-100">
                        <div class="flex items-center justify-center bg-white bg-opacity-20 rounded-lg py-2 px-3">
                            <i class="fas fa-check-circle mr-2 text-green-200"></i>
                            <span>No Credit Card</span>
                        </div>
                        <div class="flex items-center justify-center bg-white bg-opacity-20 rounded-lg py-2 px-3">
                            <i class="fas fa-bolt mr-2 text-green-200"></i>
                            <span>Instant Access</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Firebase UI Auth Container -->
            <div class="px-8 pb-6">
                <div id="firebaseui-auth-container" class="firebase-ui-container"></div>
            </div>

            <!-- Custom Signup Form (Fallback) -->
            <div id="custom-signup-form" class="px-8 pb-6 space-y-5" style="display: none;">
                <div class="space-y-4">
                    <div>
                        <label for="email" class="block text-sm font-semibold text-gray-200 mb-2">Email Address</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                            <input type="email" id="email" name="email" required
                                   class="w-full pl-10 pr-4 py-4 bg-white bg-opacity-15 border border-white border-opacity-40 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
                                   placeholder="Enter your email address">
                        </div>
                    </div>
                    <div>
                        <label for="password" class="block text-sm font-semibold text-gray-200 mb-2">Password</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input type="password" id="password" name="password" required
                                   class="w-full pl-10 pr-4 py-4 bg-white bg-opacity-15 border border-white border-opacity-40 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
                                   placeholder="Create a password (min 6 characters)">
                        </div>
                    </div>
                    <div>
                        <label for="confirm-password" class="block text-sm font-semibold text-gray-200 mb-2">Confirm Password</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-shield-alt text-gray-400"></i>
                            </div>
                            <input type="password" id="confirm-password" name="confirm-password" required
                                   class="w-full pl-10 pr-4 py-4 bg-white bg-opacity-15 border border-white border-opacity-40 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
                                   placeholder="Confirm your password">
                        </div>
                    </div>
                </div>
                <button type="submit" id="signup-btn" class="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold py-4 px-6 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300 flex items-center justify-center">
                    <i class="fas fa-user-plus mr-3 text-lg"></i>
                    <span class="text-lg">Create Account & Get 3 Free Credits</span>
                </button>
            </div>

            <!-- Error Message -->
            <div id="error-message" class="hidden mx-8 mb-4">
                <div class="bg-red-500 bg-opacity-20 border border-red-400 border-opacity-50 rounded-xl p-4 backdrop-blur-sm">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-red-300 mr-3"></i>
                        <p class="text-red-200 text-sm font-medium"></p>
                    </div>
                </div>
            </div>

            <!-- Success Message -->
            <div id="success-message" class="hidden mx-8 mb-4">
                <div class="bg-green-500 bg-opacity-20 border border-green-400 border-opacity-50 rounded-xl p-4 backdrop-blur-sm">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-300 mr-3"></i>
                        <p class="text-green-200 text-sm font-medium"></p>
                    </div>
                </div>
            </div>

            <!-- Enhanced Footer Links -->
            <div class="px-8 pb-8">
                <div class="space-y-4">
                    <!-- Sign In Link -->
                    <div class="text-center bg-white bg-opacity-10 rounded-xl p-4 backdrop-blur-sm">
                        <p class="text-gray-200 mb-2">Already have an account?</p>
                        <a href="login.html" class="inline-flex items-center text-green-300 hover:text-green-200 font-semibold text-lg transition-colors duration-300">
                            <i class="fas fa-sign-in-alt mr-2"></i>Sign in here
                        </a>
                    </div>

                    <!-- Terms -->
                    <div class="text-center bg-white bg-opacity-10 rounded-xl p-4 backdrop-blur-sm">
                        <p class="text-gray-300 text-sm mb-2">By creating an account, you agree to our</p>
                        <div class="flex justify-center space-x-4">
                            <a href="terms.html" class="text-green-300 hover:text-green-200 font-medium underline transition-colors duration-300">Terms of Service</a>
                            <span class="text-gray-400">•</span>
                            <a href="privacy.html" class="text-green-300 hover:text-green-200 font-medium underline transition-colors duration-300">Privacy Policy</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </main>

    <!-- Enhanced Footer -->
    <footer class="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-gray-300 py-12 mt-auto">
        <div class="container mx-auto px-6">
            <div class="text-center">
                <!-- Logo and Brand -->
                <div class="flex items-center justify-center mb-6">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="w-8 h-8 mr-3">
                    <span class="animated-gradient-text text-2xl font-bold">Aifrobeats</span>
                </div>

                <!-- Tagline -->
                <p class="text-lg mb-6 text-gray-200">AI-Generated Afrobeats, Human-Curated for you.</p>

                <!-- Navigation Links -->
                <div class="flex justify-center space-x-8 mb-6 text-sm">
                    <a href="terms.html" class="hover:text-green-400 transition-colors duration-300 flex items-center">
                        <i class="fas fa-file-contract mr-2"></i>Terms of Service
                    </a>
                    <a href="privacy.html" class="hover:text-green-400 transition-colors duration-300 flex items-center">
                        <i class="fas fa-shield-alt mr-2"></i>Privacy Policy
                    </a>
                    <a href="index.html" class="hover:text-green-400 transition-colors duration-300 flex items-center">
                        <i class="fas fa-home mr-2"></i>Back to Home
                    </a>
                </div>

                <!-- Copyright -->
                <div class="border-t border-gray-700 pt-6">
                    <p class="text-sm text-gray-400">&copy; <span id="currentYear"></span> AIFROBEATS - All Rights Reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
            <span class="text-gray-700">Creating your account...</span>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/ui/6.0.1/firebase-ui-auth.js"></script>

    <!-- Signup Script -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCRlkgzE2FMFFywISBm4GO4pfZ5pUFql0o",
            authDomain: "aifrobeats-com.firebaseapp.com",
            projectId: "aifrobeats-com",
            storageBucket: "aifrobeats-com.firebasestorage.app",
            messagingSenderId: "*************",
            appId: "1:*************:web:95074e13e4d40adaa164fa",
            measurementId: "G-YCQLRG8L44"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        // Firebase UI configuration
        const uiConfig = {
            signInSuccessUrl: 'dashboard.html',
            signInOptions: [
                {
                    provider: firebase.auth.EmailAuthProvider.PROVIDER_ID,
                    requireDisplayName: false,
                    signInMethod: firebase.auth.EmailAuthProvider.EMAIL_PASSWORD_SIGN_IN_METHOD
                }
            ],
            tosUrl: 'terms.html',
            privacyPolicyUrl: 'privacy.html',
            callbacks: {
                signInSuccessWithAuthResult: function(authResult, redirectUrl) {
                    // User successfully signed up
                    const user = authResult.user;
                    console.log('User signed up:', user.uid, user.email);

                    // Show loading
                    showLoading(true);

                    // Create user document with 3 free credits
                    return db.collection('users').doc(user.uid).set({
                        email: user.email,
                        credits: 3,
                        createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                        requests: [],
                        profile: {
                            displayName: user.displayName || '',
                            photoURL: user.photoURL || ''
                        }
                    }, { merge: true }).then(() => {
                        console.log('User document created successfully');
                        showLoading(false);
                        showSuccess('Account created! You have 3 free preview credits. Redirecting...');
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 2000);
                        return false; // Handle redirect manually
                    }).catch((error) => {
                        console.error('Error creating user document:', error);
                        showLoading(false);

                        // Even if Firestore fails, the user is created in Auth
                        // So we should still redirect but show a warning
                        showSuccess('Account created! Redirecting to dashboard...');
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 2000);
                        return false; // Handle redirect manually
                    });
                },
                signInFailure: function(error) {
                    console.error('Sign-up error:', error);
                    showLoading(false);

                    if (error.code === 'auth/email-already-in-use') {
                        showError('This email is already registered. Please sign in instead or use a different email.');
                    } else if (error.code === 'auth/weak-password') {
                        showError('Password is too weak. Please use at least 6 characters.');
                    } else if (error.code === 'auth/invalid-email') {
                        showError('Please enter a valid email address.');
                    } else if (error.code === 'auth/operation-not-allowed') {
                        showError('Email/password accounts are not enabled. Please contact support.');
                    } else {
                        showError('Sign-up failed: ' + (error.message || 'Please try again.'));
                    }

                    return Promise.resolve();
                }
            }
        };

        // Initialize Firebase UI
        const ui = new firebaseui.auth.AuthUI(auth);

        // Check if user is already signed in
        auth.onAuthStateChanged(async (user) => {
            if (user) {
                console.log('User already signed in:', user.uid);

                // Check if user document exists and create if missing
                try {
                    const userDoc = await db.collection('users').doc(user.uid).get();
                    if (!userDoc.exists) {
                        console.log('User document missing, creating...');
                        await db.collection('users').doc(user.uid).set({
                            email: user.email,
                            credits: 3,
                            createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                            requests: [],
                            profile: {
                                displayName: user.displayName || '',
                                photoURL: user.photoURL || ''
                            }
                        });
                        console.log('User document created for existing user');
                    }
                } catch (error) {
                    console.error('Error checking/creating user document:', error);
                }

                // User is signed in, redirect to dashboard
                window.location.href = 'dashboard.html';
            } else {
                // User is not signed in, show signup form
                ui.start('#firebaseui-auth-container', uiConfig);
            }
        });

        // Utility functions
        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.querySelector('p').textContent = message;
            errorDiv.classList.remove('hidden');
            setTimeout(() => errorDiv.classList.add('hidden'), 5000);
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('success-message');
            successDiv.querySelector('p').textContent = message;
            successDiv.classList.remove('hidden');
            setTimeout(() => successDiv.classList.add('hidden'), 5000);
        }

        function showLoading(show = true) {
            const overlay = document.getElementById('loading-overlay');
            if (show) {
                overlay.classList.remove('hidden');
            } else {
                overlay.classList.add('hidden');
            }
        }

        // Set current year in footer
        document.getElementById('currentYear').textContent = new Date().getFullYear();
    </script>
</body>
</html>
