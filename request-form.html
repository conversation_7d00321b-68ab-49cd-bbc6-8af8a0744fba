<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Request Custom Song - Aifrobeats</title>
    <link rel="icon" href="images/mlogo.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">

    <style>
        /* Enhanced Request Form Styles */
        .form-section {
            @apply bg-white rounded-2xl shadow-lg p-8 mb-8;
        }

        .form-group {
            @apply mb-6;
        }

        .form-label {
            @apply block text-sm font-semibold text-gray-700 mb-2;
        }

        .form-input {
            @apply w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-green-500 transition-all duration-300;
        }

        .form-input:focus {
            @apply shadow-lg transform scale-105;
        }

        .form-select {
            @apply w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-green-500 transition-all duration-300 bg-white;
        }

        .form-textarea {
            @apply w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-green-500 transition-all duration-300 resize-none;
        }

        .credit-indicator {
            @apply bg-gradient-to-r from-green-500 to-blue-500 text-white px-6 py-3 rounded-xl shadow-lg;
        }

        .insufficient-credits-warning {
            @apply bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-2xl p-6 shadow-xl;
        }

        .form-step {
            @apply opacity-50 transform scale-95 transition-all duration-500;
        }

        .form-step.active {
            @apply opacity-100 transform scale-100;
        }

        .progress-bar {
            @apply w-full bg-gray-200 rounded-full h-2 mb-8;
        }

        .progress-fill {
            @apply bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-500;
        }

        .floating-label {
            @apply absolute left-4 top-3 text-gray-500 transition-all duration-300 pointer-events-none;
        }

        .form-input:focus + .floating-label,
        .form-input:not(:placeholder-shown) + .floating-label {
            @apply transform -translate-y-6 scale-75 text-green-500;
        }
    </style>
</head>
<body class="min-h-screen" style="background-color: var(--bg-primary);">
    
    <!-- Navigation Header -->
    <header class="shadow-sm sticky top-0 z-50" style="background-color: var(--header-bg);">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.html" class="logo-container text-2xl font-bold text-transparent flex items-center">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="site-logo">
                    <span class="animated-gradient-text">Aifrobeats</span>
                </a>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2 bg-green-100 px-3 py-1 rounded-full">
                    <i class="fas fa-coins text-green-600"></i>
                    <span id="user-credits" class="font-semibold text-green-700">0</span>
                    <span class="text-green-600 text-sm">credits</span>
                </div>
                <a href="dashboard.html" class="text-gray-600 hover:text-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-1"></i>Back to Dashboard
                </a>
                <button id="logout-btn" class="text-red-600 hover:text-red-700 transition-colors">
                    <i class="fas fa-sign-out-alt mr-1"></i>Logout
                </button>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">

        <!-- Enhanced Header -->
        <div class="text-center mb-12">
            <div class="bg-gradient-to-r from-green-500 to-blue-600 rounded-3xl p-8 text-white shadow-2xl mb-8">
                <div class="flex items-center justify-center mb-4">
                    <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-music text-3xl"></i>
                    </div>
                    <div class="text-left">
                        <h1 class="text-4xl font-bold mb-2">Create Your Custom Song</h1>
                        <p class="text-green-100 text-lg">Tell us your vision, we'll make it reality</p>
                    </div>
                </div>

                <!-- Credit Status -->
                <div class="bg-white bg-opacity-20 rounded-xl p-4 mt-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-coins text-yellow-300 text-2xl mr-3"></i>
                            <div>
                                <div class="text-sm text-green-100">Available Credits</div>
                                <div class="text-2xl font-bold" id="header-credits">0</div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-green-100">Cost per Request</div>
                            <div class="text-2xl font-bold">1 Credit</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Credit Check Warning -->
        <div id="insufficient-credits" class="hidden insufficient-credits-warning mb-8">
            <div class="flex items-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-exclamation-triangle text-2xl"></i>
                </div>
                <div class="flex-1">
                    <h3 class="text-xl font-bold mb-2">Insufficient Credits</h3>
                    <p class="text-white text-opacity-90 mb-4">You need at least 1 credit to request a custom song. Purchase more credits to continue creating amazing music!</p>
                    <button onclick="buyCredits()" class="bg-white text-red-600 font-semibold px-6 py-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-shopping-cart mr-2"></i>Buy Credits Now
                    </button>
                </div>
            </div>
        </div>

        <!-- Progress Indicator -->
        <div class="max-w-2xl mx-auto mb-8">
            <div class="progress-bar">
                <div id="progress-fill" class="progress-fill" style="width: 0%"></div>
            </div>
            <div class="flex justify-between text-sm text-gray-600">
                <span>Song Details</span>
                <span>Contact Info</span>
                <span>Submit Request</span>
            </div>
        </div>

        <!-- Enhanced Request Form -->
        <div class="max-w-4xl mx-auto">
            <form id="request-form" class="space-y-8">

                <!-- Step 1: Song Details -->
                <div class="form-section form-step active" id="step-1">
                    <div class="flex items-center mb-6">
                        <div class="w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center font-bold mr-4">1</div>
                        <h2 class="text-2xl font-bold text-gray-800">Song Details</h2>
                    </div>

                    <div class="grid md:grid-cols-2 gap-6">
                        <!-- Song Title -->
                        <div class="form-group">
                            <label for="song-title" class="form-label">
                                Song Title <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="song-title" name="songTitle" required
                                   class="form-input"
                                   placeholder="What would you like to call your song?">
                        </div>

                        <!-- Song Type -->
                        <div class="form-group">
                            <label for="song-type" class="form-label">
                                Song Type <span class="text-red-500">*</span>
                            </label>
                            <select id="song-type" name="songType" required class="form-select">
                                <option value="">Select song type...</option>
                                <option value="wedding">💒 Wedding Song</option>
                                <option value="birthday">🎂 Birthday Song</option>
                                <option value="love-song">💕 Love Song</option>
                                <option value="jingle">📢 Business Jingle</option>
                                <option value="fitness">💪 Fitness/Workout</option>
                                <option value="corporate">🏢 Corporate Event</option>
                                <option value="club">🎉 Club Anthem</option>
                                <option value="custom">✨ Custom/Other</option>
                            </select>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="form-group">
                        <label for="description" class="form-label">
                            Describe Your Vision <span class="text-red-500">*</span>
                        </label>
                        <textarea id="description" name="description" required rows="4"
                                  class="form-textarea"
                                  placeholder="Tell us about the occasion, mood, names to include, specific lyrics, or any other details that will help us create the perfect song for you..."></textarea>
                        <div class="flex items-center mt-2 text-sm text-gray-600">
                            <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                            <span>The more details you provide, the better your song will be!</span>
                        </div>
                    </div>

                    <div class="grid md:grid-cols-2 gap-6">
                        <!-- Mood/Style -->
                        <div class="form-group">
                            <label for="mood" class="form-label">Mood/Style</label>
                            <select id="mood" name="mood" class="form-select">
                                <option value="">Select mood (optional)...</option>
                                <option value="upbeat">🚀 Upbeat & Energetic</option>
                                <option value="romantic">💖 Romantic & Smooth</option>
                                <option value="celebratory">🎊 Celebratory & Joyful</option>
                                <option value="motivational">💪 Motivational & Inspiring</option>
                                <option value="chill">😌 Chill & Relaxed</option>
                                <option value="party">🎉 Party & Dance</option>
                            </select>
                        </div>

                        <!-- Duration -->
                        <div class="form-group">
                            <label for="duration" class="form-label">Preferred Duration</label>
                            <select id="duration" name="duration" class="form-select">
                                <option value="">Select duration (optional)...</option>
                                <option value="30-60">30-60 seconds (Short)</option>
                                <option value="60-120">1-2 minutes (Standard)</option>
                                <option value="120-180">2-3 minutes (Extended)</option>
                                <option value="custom">Custom length</option>
                            </select>
                        </div>
                    </div>

                    <!-- Special Instructions -->
                    <div class="form-group">
                        <label for="special-instructions" class="form-label">Special Instructions</label>
                        <textarea id="special-instructions" name="specialInstructions" rows="3"
                                  class="form-textarea"
                                  placeholder="Any specific requirements, names to avoid, cultural references, or other special requests..."></textarea>
                    </div>
                </div>

                <!-- Step 2: Contact & Delivery -->
                <div class="form-section form-step" id="step-2">
                    <div class="flex items-center mb-6">
                        <div class="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-4">2</div>
                        <h2 class="text-2xl font-bold text-gray-800">Contact & Delivery</h2>
                    </div>

                    <div class="grid md:grid-cols-2 gap-6">
                        <!-- Contact Preference -->
                        <div class="form-group">
                            <label for="contact-method" class="form-label">
                                How would you like to receive your song? <span class="text-red-500">*</span>
                            </label>
                            <select id="contact-method" name="contactMethod" required class="form-select">
                                <option value="">Select delivery method...</option>
                                <option value="email">📧 Email</option>
                                <option value="whatsapp">📱 WhatsApp</option>
                                <option value="telegram">💬 Telegram</option>
                            </select>
                        </div>

                        <!-- Contact Info -->
                        <div class="form-group" id="contact-info-section">
                            <label for="contact-info" class="form-label">
                                <span id="contact-label">Contact Information</span> <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="contact-info" name="contactInfo" required
                                   class="form-input"
                                   placeholder="Enter your contact information">
                            <p id="contact-help" class="text-sm text-gray-600 mt-2 flex items-center">
                                <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                <span>We'll send your completed song to this address</span>
                            </p>
                        </div>
                    </div>

                    <!-- Delivery Preferences -->
                    <div class="form-group">
                        <label class="form-label">Delivery Preferences</label>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div class="bg-gray-50 rounded-xl p-4">
                                <div class="flex items-center mb-2">
                                    <input type="checkbox" id="preview-notification" name="previewNotification" class="mr-3">
                                    <label for="preview-notification" class="font-medium text-gray-700">Preview Notification</label>
                                </div>
                                <p class="text-sm text-gray-600">Get notified when your preview is ready</p>
                            </div>
                            <div class="bg-gray-50 rounded-xl p-4">
                                <div class="flex items-center mb-2">
                                    <input type="checkbox" id="completion-notification" name="completionNotification" class="mr-3" checked>
                                    <label for="completion-notification" class="font-medium text-gray-700">Completion Notification</label>
                                </div>
                                <p class="text-sm text-gray-600">Get notified when your song is complete</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Review & Submit -->
                <div class="form-section form-step" id="step-3">
                    <div class="flex items-center mb-6">
                        <div class="w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center font-bold mr-4">3</div>
                        <h2 class="text-2xl font-bold text-gray-800">Review & Submit</h2>
                    </div>

                    <!-- Request Summary -->
                    <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 mb-6">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">Request Summary</h3>
                        <div id="request-summary" class="space-y-2 text-sm text-gray-700">
                            <!-- Summary will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Cost Breakdown -->
                    <div class="bg-white border-2 border-gray-200 rounded-2xl p-6 mb-6">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">Cost Breakdown</h3>
                        <div class="flex justify-between items-center py-2">
                            <span>Custom Song Request</span>
                            <span class="font-semibold">1 Credit</span>
                        </div>
                        <div class="border-t border-gray-200 pt-2 mt-2">
                            <div class="flex justify-between items-center font-bold text-lg">
                                <span>Total Cost</span>
                                <span class="text-green-600">1 Credit</span>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" id="submit-btn" class="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white font-bold py-4 px-12 rounded-2xl shadow-xl transform hover:scale-105 transition-all duration-300 text-lg">
                            <i class="fas fa-music mr-3"></i>Submit Request (1 Credit)
                        </button>
                        <p class="text-sm text-gray-600 mt-4 flex items-center justify-center">
                            <i class="fas fa-clock text-green-500 mr-2"></i>
                            <span>Your song will be ready in 1-3 hours</span>
                        </p>
                    </div>
                </div>

                <!-- Form Navigation -->
                <div class="flex justify-between items-center pt-8">
                    <button type="button" id="prev-btn" class="bg-gray-500 hover:bg-gray-600 text-white font-semibold py-3 px-6 rounded-xl transition-colors duration-300 hidden">
                        <i class="fas fa-arrow-left mr-2"></i>Previous
                    </button>
                    <button type="button" id="next-btn" class="bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-6 rounded-xl transition-colors duration-300">
                        Next <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                </div>
            </form>
        </div>
    </main>

    <!-- Footer -->
    <footer id="contact" class="site-footer bg-gray-900 text-gray-400 py-12 md:py-16">
        <div class="container mx-auto px-4 md:px-6 footer-container">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-10">
                <!-- Company Info -->
                <div class="footer-col">
                    <h3 class="text-xl md:text-2xl font-semibold text-white mb-4 flex items-center">
                        <img src="images/mlogo.png" alt="Aifrobeats Logo" class="w-8 h-8 mr-2">
                        <span class="animated-gradient-text">Aifrobeats</span>
                    </h3>
                    <p class="mb-4 text-sm md:text-base">AI-Generated Afrobeats, Human-Curated for you. Fast, affordable, and unique music for every occasion.</p>
                    <div class="flex items-center mb-3">
                        <i class="fas fa-envelope text-green-500 mr-3"></i>
                        <a href="mailto:<EMAIL>" class="hover:text-green-400 transition-colors"><EMAIL></a>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-phone-alt text-green-500 mr-3"></i>
                        <a href="tel:+2347038808350" class="hover:text-green-400 transition-colors">+234 ************</a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="footer-col">
                    <h3 class="text-lg md:text-xl font-semibold text-white mb-4">Quick Links</h3>
                    <ul class="space-y-2 footer-links">
                        <li>
                            <a href="index.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Home
                            </a>
                        </li>
                        <li>
                            <a href="dashboard.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Dashboard
                            </a>
                        </li>
                        <li>
                            <a href="music-library.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Music Library
                            </a>
                        </li>
                        <li>
                            <a href="request-form.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Request Song
                            </a>
                        </li>
                        <li>
                            <a href="index.html#pricing" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Pricing
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Connect With Us -->
                <div class="footer-col">
                    <h3 class="text-lg md:text-xl font-semibold text-white mb-4">Connect With Us</h3>
                    <p class="mb-3 text-sm md:text-base">Follow us for updates and examples!</p>
                    <div class="flex space-x-3 mb-5">
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-soundcloud"></i>
                        </a>
                    </div>
                    <div class="bg-gray-800 p-3 rounded-lg newsletter-form">
                        <h4 class="text-white text-sm font-semibold mb-2">Subscribe to our newsletter</h4>
                        <form class="flex">
                            <input type="email" placeholder="Your email" class="form-input bg-gray-700 border-0 text-white text-sm rounded-l-lg focus:ring-green-500 focus:border-green-500 flex-grow">
                            <button type="submit" class="bg-green-600 text-white px-3 rounded-r-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Copyright -->
            <div class="mt-6 md:mt-8 pt-5 md:pt-6 border-t border-gray-700 text-center text-xs md:text-sm">
                <p>&copy; <span id="currentYear"></span> AIFROBEATS CUSTOM MUSIC PRODUCTIONS – All Rights Reserved.</p>
                <div class="mt-2 flex flex-wrap justify-center gap-4">
                    <a href="terms.html" class="hover:text-green-400 transition-colors">Terms of Service</a>
                    <span class="hidden md:inline">|</span>
                    <a href="privacy.html" class="hover:text-green-400 transition-colors">Privacy Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Success Modal -->
    <div id="success-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-8 max-w-md mx-4 text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold mb-2">Request Submitted!</h3>
            <p class="text-gray-600 mb-4">Your custom song request has been submitted successfully. We'll have your track ready in 1-3 hours.</p>
            <div class="space-y-3">
                <a href="dashboard.html" class="primary-button w-full">
                    <i class="fas fa-tachometer-alt mr-2"></i>Go to Dashboard
                </a>
                <button onclick="closeSuccessModal()" class="secondary-button w-full">
                    Create Another Request
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
            <span class="text-gray-700">Submitting your request...</span>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>

    <!-- Request Form Script -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCRlkgzE2FMFFywISBm4GO4pfZ5pUFql0o",
            authDomain: "aifrobeats-com.firebaseapp.com",
            projectId: "aifrobeats-com",
            storageBucket: "aifrobeats-com.firebasestorage.app",
            messagingSenderId: "1013326896271",
            appId: "1:1013326896271:web:95074e13e4d40adaa164fa",
            measurementId: "G-YCQLRG8L44"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        let currentUser = null;
        let userCredits = 0;
        let currentStep = 1;
        const totalSteps = 3;

        // Auth state observer
        auth.onAuthStateChanged(async (user) => {
            if (user) {
                currentUser = user;
                await loadUserData();
                initializeForm();
            } else {
                // User is not signed in, redirect to login
                window.location.href = 'login.html';
            }
        });

        // Enhanced real-time user data loading
        function loadUserData() {
            if (!currentUser) return;

            // Set up real-time listener for user data
            const userDocRef = db.collection('users').doc(currentUser.uid);
            userDocRef.onSnapshot(async (doc) => {
                try {
                    if (doc.exists) {
                        const userData = doc.data();
                        userCredits = userData.credits || 0;

                        // Update multiple credit displays
                        document.getElementById('user-credits').textContent = userCredits;
                        document.getElementById('header-credits').textContent = userCredits;

                        // Check if user has sufficient credits
                        checkCreditAvailability();

                        console.log('User credits updated:', userCredits);
                    } else {
                        console.log('User document does not exist, creating...');
                        // Create user document with 3 credits if it doesn't exist
                        await userDocRef.set({
                            email: currentUser.email,
                            credits: 3,
                            createdAt: firebase.firestore.FieldValue.serverTimestamp()
                        });
                    }
                } catch (error) {
                    console.error('Error in user data snapshot:', error);
                }
            }, (error) => {
                console.error('Error loading user data:', error);
                alert('Error loading your account data. Please refresh the page.');
            });
        }

        // Check credit availability
        function checkCreditAvailability() {
            const insufficientCreditsDiv = document.getElementById('insufficient-credits');
            const submitBtn = document.getElementById('submit-btn');
            const nextBtn = document.getElementById('next-btn');

            if (userCredits < 1) {
                insufficientCreditsDiv.classList.remove('hidden');
                submitBtn.disabled = true;
                submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
                if (currentStep === totalSteps) {
                    nextBtn.disabled = true;
                    nextBtn.classList.add('opacity-50', 'cursor-not-allowed');
                }
            } else {
                insufficientCreditsDiv.classList.add('hidden');
                submitBtn.disabled = false;
                submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                nextBtn.disabled = false;
                nextBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            }
        }

        // Initialize form functionality
        function initializeForm() {
            updateProgressBar();
            updateStepVisibility();
            setupFormNavigation();
            setupContactMethodHandler();
            setupFormValidation();
        }

        // Multi-step form navigation
        function setupFormNavigation() {
            const nextBtn = document.getElementById('next-btn');
            const prevBtn = document.getElementById('prev-btn');

            nextBtn.addEventListener('click', () => {
                if (validateCurrentStep()) {
                    if (currentStep < totalSteps) {
                        currentStep++;
                        updateStepVisibility();
                        updateProgressBar();
                        updateNavigationButtons();
                        if (currentStep === totalSteps) {
                            updateRequestSummary();
                        }
                    }
                }
            });

            prevBtn.addEventListener('click', () => {
                if (currentStep > 1) {
                    currentStep--;
                    updateStepVisibility();
                    updateProgressBar();
                    updateNavigationButtons();
                }
            });
        }

        // Update step visibility
        function updateStepVisibility() {
            for (let i = 1; i <= totalSteps; i++) {
                const step = document.getElementById(`step-${i}`);
                if (i === currentStep) {
                    step.classList.add('active');
                    step.classList.remove('hidden');
                } else {
                    step.classList.remove('active');
                    step.classList.add('hidden');
                }
            }
        }

        // Update progress bar
        function updateProgressBar() {
            const progressFill = document.getElementById('progress-fill');
            const progress = (currentStep / totalSteps) * 100;
            progressFill.style.width = `${progress}%`;
        }

        // Update navigation buttons
        function updateNavigationButtons() {
            const nextBtn = document.getElementById('next-btn');
            const prevBtn = document.getElementById('prev-btn');

            // Show/hide previous button
            if (currentStep === 1) {
                prevBtn.classList.add('hidden');
            } else {
                prevBtn.classList.remove('hidden');
            }

            // Update next button text and visibility
            if (currentStep === totalSteps) {
                nextBtn.classList.add('hidden');
            } else {
                nextBtn.classList.remove('hidden');
                nextBtn.innerHTML = `Next <i class="fas fa-arrow-right ml-2"></i>`;
            }
        }

        // Validate current step
        function validateCurrentStep() {
            const currentStepElement = document.getElementById(`step-${currentStep}`);
            const requiredFields = currentStepElement.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('border-red-500');
                    isValid = false;
                } else {
                    field.classList.remove('border-red-500');
                }
            });

            if (!isValid) {
                alert('Please fill in all required fields before proceeding.');
            }

            return isValid;
        }

        // Setup contact method handler
        function setupContactMethodHandler() {
            document.getElementById('contact-method').addEventListener('change', function() {
                const method = this.value;
                const contactInfo = document.getElementById('contact-info');
                const contactLabel = document.getElementById('contact-label');
                const contactHelp = document.getElementById('contact-help');

                switch(method) {
                    case 'email':
                        contactInfo.placeholder = 'Enter your email address';
                        contactInfo.type = 'email';
                        contactLabel.textContent = 'Email Address';
                        contactHelp.innerHTML = '<i class="fas fa-envelope text-blue-500 mr-2"></i><span>We\'ll send your completed song to this email</span>';
                        break;
                    case 'whatsapp':
                        contactInfo.placeholder = 'Enter your WhatsApp number (with country code)';
                        contactInfo.type = 'tel';
                        contactLabel.textContent = 'WhatsApp Number';
                        contactHelp.innerHTML = '<i class="fab fa-whatsapp text-green-500 mr-2"></i><span>Include country code (e.g., +1234567890)</span>';
                        break;
                    case 'telegram':
                        contactInfo.placeholder = 'Enter your Telegram username';
                        contactInfo.type = 'text';
                        contactLabel.textContent = 'Telegram Username';
                        contactHelp.innerHTML = '<i class="fab fa-telegram text-blue-500 mr-2"></i><span>Your Telegram username (without @)</span>';
                        break;
                    default:
                        contactInfo.placeholder = 'Enter your contact information';
                        contactInfo.type = 'text';
                        contactLabel.textContent = 'Contact Information';
                        contactHelp.innerHTML = '<i class="fas fa-info-circle text-blue-500 mr-2"></i><span>We\'ll send your completed song to this address</span>';
                }
            });
        }

        // Setup form validation
        function setupFormValidation() {
            const form = document.getElementById('request-form');
            const inputs = form.querySelectorAll('input, select, textarea');

            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    if (input.hasAttribute('required') && !input.value.trim()) {
                        input.classList.add('border-red-500');
                    } else {
                        input.classList.remove('border-red-500');
                    }
                });

                input.addEventListener('input', () => {
                    if (input.classList.contains('border-red-500') && input.value.trim()) {
                        input.classList.remove('border-red-500');
                    }
                });
            });
        }

        // Update request summary
        function updateRequestSummary() {
            const summaryDiv = document.getElementById('request-summary');
            const formData = new FormData(document.getElementById('request-form'));

            const summary = [
                { label: 'Song Title', value: formData.get('songTitle') || 'Not specified' },
                { label: 'Song Type', value: formData.get('songType') || 'Not specified' },
                { label: 'Mood/Style', value: formData.get('mood') || 'Not specified' },
                { label: 'Duration', value: formData.get('duration') || 'Standard' },
                { label: 'Delivery Method', value: formData.get('contactMethod') || 'Not specified' },
                { label: 'Contact Info', value: formData.get('contactInfo') || 'Not specified' }
            ];

            summaryDiv.innerHTML = summary.map(item =>
                `<div class="flex justify-between py-1">
                    <span class="font-medium">${item.label}:</span>
                    <span class="text-gray-600">${item.value}</span>
                </div>`
            ).join('');
        }

        // Enhanced form submission
        document.getElementById('request-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            // Final validation
            if (userCredits < 1) {
                alert('You need at least 1 credit to submit a request.');
                return;
            }

            if (!validateCurrentStep()) {
                return;
            }

            showLoading(true);

            try {
                const formData = new FormData(e.target);
                const requestData = {
                    songTitle: formData.get('songTitle'),
                    songType: formData.get('songType'),
                    description: formData.get('description'),
                    mood: formData.get('mood'),
                    duration: formData.get('duration'),
                    specialInstructions: formData.get('specialInstructions'),
                    contactMethod: formData.get('contactMethod'),
                    contactInfo: formData.get('contactInfo'),
                    previewNotification: formData.get('previewNotification') === 'on',
                    completionNotification: formData.get('completionNotification') === 'on',
                    userEmail: currentUser.email,
                    submittedAt: new Date().toISOString()
                };

                // Create the request with enhanced data
                const requestRef = await db.collection('requests').add({
                    userId: currentUser.uid,
                    ...requestData,
                    status: 'pending',
                    priority: 'normal',
                    estimatedCompletion: calculateEstimatedCompletion(),
                    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                    updatedAt: firebase.firestore.FieldValue.serverTimestamp()
                });

                // Deduct credit from user with transaction
                await db.runTransaction(async (transaction) => {
                    const userRef = db.collection('users').doc(currentUser.uid);
                    const userDoc = await transaction.get(userRef);

                    if (!userDoc.exists) {
                        throw new Error('User document does not exist');
                    }

                    const currentCredits = userDoc.data().credits || 0;
                    if (currentCredits < 1) {
                        throw new Error('Insufficient credits');
                    }

                    transaction.update(userRef, {
                        credits: currentCredits - 1,
                        lastRequestAt: firebase.firestore.FieldValue.serverTimestamp()
                    });
                });

                showLoading(false);
                showSuccessModal();

                // Reset form and go back to step 1
                e.target.reset();
                currentStep = 1;
                updateStepVisibility();
                updateProgressBar();
                updateNavigationButtons();

                console.log('Request submitted successfully:', requestRef.id);

            } catch (error) {
                console.error('Error submitting request:', error);
                showLoading(false);

                if (error.message === 'Insufficient credits') {
                    alert('You don\'t have enough credits to submit this request. Please purchase more credits.');
                } else {
                    alert('There was an error submitting your request. Please try again.');
                }
            }
        });

        // Calculate estimated completion time
        function calculateEstimatedCompletion() {
            const now = new Date();
            const estimatedHours = 2; // Default 2 hours
            const completionTime = new Date(now.getTime() + (estimatedHours * 60 * 60 * 1000));
            return completionTime.toISOString();
        }

        // Buy credits function
        function buyCredits() {
            alert('Credit purchase feature coming soon! Contact support for immediate assistance.');
        }

        // Utility functions
        function showLoading(show = true) {
            const overlay = document.getElementById('loading-overlay');
            if (show) {
                overlay.classList.remove('hidden');
            } else {
                overlay.classList.add('hidden');
            }
        }

        function showSuccessModal() {
            document.getElementById('success-modal').classList.remove('hidden');
        }

        function closeSuccessModal() {
            document.getElementById('success-modal').classList.add('hidden');
            loadUserData(); // Refresh credits
        }

        // Logout function
        document.getElementById('logout-btn').addEventListener('click', async () => {
            try {
                await auth.signOut();
                window.location.href = 'index.html';
            } catch (error) {
                console.error('Error signing out:', error);
            }
        });

        // Set current year in footer
        document.getElementById('currentYear').textContent = new Date().getFullYear();
    </script>
</body>
</html>
