<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Request Custom Song - Aifrobeats</title>
    <link rel="icon" href="images/mlogo.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="min-h-screen" style="background-color: var(--bg-primary);">
    
    <!-- Navigation Header -->
    <header class="shadow-sm sticky top-0 z-50" style="background-color: var(--header-bg);">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.html" class="logo-container text-2xl font-bold text-transparent flex items-center">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="site-logo">
                    <span class="animated-gradient-text">Aifrobeats</span>
                </a>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2 bg-green-100 px-3 py-1 rounded-full">
                    <i class="fas fa-coins text-green-600"></i>
                    <span id="user-credits" class="font-semibold text-green-700">0</span>
                    <span class="text-green-600 text-sm">credits</span>
                </div>
                <a href="dashboard.html" class="text-gray-600 hover:text-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-1"></i>Back to Dashboard
                </a>
                <button id="logout-btn" class="text-red-600 hover:text-red-700 transition-colors">
                    <i class="fas fa-sign-out-alt mr-1"></i>Logout
                </button>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold theme-text mb-2">Request Your Custom Song</h1>
            <p class="theme-text-muted">Tell us about your vision and we'll create the perfect Afrobeats track for you</p>
        </div>

        <!-- Credit Check Warning -->
        <div id="insufficient-credits" class="hidden bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-red-600 mr-3"></i>
                <div>
                    <h3 class="font-semibold text-red-800">Insufficient Credits</h3>
                    <p class="text-red-700 text-sm">You need at least 1 credit to request a song. Please purchase more credits to continue.</p>
                </div>
            </div>
        </div>

        <!-- Request Form -->
        <div class="max-w-2xl mx-auto">
            <form id="request-form" class="card space-y-6">
                
                <!-- Song Title -->
                <div>
                    <label for="song-title" class="block text-sm font-medium theme-text mb-2">
                        Song Title <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="song-title" name="songTitle" required 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="What would you like to call your song?">
                </div>

                <!-- Song Type -->
                <div>
                    <label for="song-type" class="block text-sm font-medium theme-text mb-2">
                        Song Type <span class="text-red-500">*</span>
                    </label>
                    <select id="song-type" name="songType" required 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        <option value="">Select song type...</option>
                        <option value="wedding">Wedding Song</option>
                        <option value="birthday">Birthday Song</option>
                        <option value="love-song">Love Song</option>
                        <option value="jingle">Business Jingle</option>
                        <option value="fitness">Fitness/Workout</option>
                        <option value="corporate">Corporate Event</option>
                        <option value="club">Club Anthem</option>
                        <option value="custom">Custom/Other</option>
                    </select>
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium theme-text mb-2">
                        Describe Your Vision <span class="text-red-500">*</span>
                    </label>
                    <textarea id="description" name="description" required rows="4"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              placeholder="Tell us about the occasion, mood, names to include, specific lyrics, or any other details that will help us create the perfect song for you..."></textarea>
                    <p class="text-sm text-gray-500 mt-1">The more details you provide, the better your song will be!</p>
                </div>

                <!-- Mood/Style -->
                <div>
                    <label for="mood" class="block text-sm font-medium theme-text mb-2">
                        Mood/Style
                    </label>
                    <select id="mood" name="mood" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        <option value="">Select mood (optional)...</option>
                        <option value="upbeat">Upbeat & Energetic</option>
                        <option value="romantic">Romantic & Smooth</option>
                        <option value="celebratory">Celebratory & Joyful</option>
                        <option value="motivational">Motivational & Inspiring</option>
                        <option value="chill">Chill & Relaxed</option>
                        <option value="party">Party & Dance</option>
                    </select>
                </div>

                <!-- Special Instructions -->
                <div>
                    <label for="special-instructions" class="block text-sm font-medium theme-text mb-2">
                        Special Instructions
                    </label>
                    <textarea id="special-instructions" name="specialInstructions" rows="3"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              placeholder="Any specific requirements, names to avoid, cultural references, or other special requests..."></textarea>
                </div>

                <!-- Contact Preference -->
                <div>
                    <label for="contact-method" class="block text-sm font-medium theme-text mb-2">
                        How would you like to receive your song? <span class="text-red-500">*</span>
                    </label>
                    <select id="contact-method" name="contactMethod" required 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        <option value="">Select delivery method...</option>
                        <option value="email">Email</option>
                        <option value="whatsapp">WhatsApp</option>
                        <option value="telegram">Telegram</option>
                    </select>
                </div>

                <!-- Contact Info -->
                <div id="contact-info-section">
                    <label for="contact-info" class="block text-sm font-medium theme-text mb-2">
                        <span id="contact-label">Contact Information</span> <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="contact-info" name="contactInfo" required 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="Enter your contact information">
                    <p id="contact-help" class="text-sm text-gray-500 mt-1">We'll send your completed song to this address</p>
                </div>

                <!-- Submit Button -->
                <div class="text-center pt-4">
                    <button type="submit" id="submit-btn" class="primary-button text-lg px-8 py-3">
                        <i class="fas fa-music mr-2"></i>Submit Request (1 Credit)
                    </button>
                    <p class="text-sm text-gray-500 mt-2">Your song will be ready in 1-3 hours</p>
                </div>
            </form>
        </div>
    </main>

    <!-- Success Modal -->
    <div id="success-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-8 max-w-md mx-4 text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold mb-2">Request Submitted!</h3>
            <p class="text-gray-600 mb-4">Your custom song request has been submitted successfully. We'll have your track ready in 1-3 hours.</p>
            <div class="space-y-3">
                <a href="dashboard.html" class="primary-button w-full">
                    <i class="fas fa-tachometer-alt mr-2"></i>Go to Dashboard
                </a>
                <button onclick="closeSuccessModal()" class="secondary-button w-full">
                    Create Another Request
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
            <span class="text-gray-700">Submitting your request...</span>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>

    <!-- Request Form Script -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "YOUR_API_KEY_HERE",
            authDomain: "YOUR_PROJECT_ID.firebaseapp.com",
            projectId: "YOUR_PROJECT_ID",
            storageBucket: "YOUR_PROJECT_ID.appspot.com",
            messagingSenderId: "YOUR_SENDER_ID",
            appId: "YOUR_APP_ID"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        let currentUser = null;
        let userCredits = 0;

        // Auth state observer
        auth.onAuthStateChanged(async (user) => {
            if (user) {
                currentUser = user;
                await loadUserData();
            } else {
                // User is not signed in, redirect to login
                window.location.href = 'login.html';
            }
        });

        // Load user data
        async function loadUserData() {
            try {
                const userDoc = await db.collection('users').doc(currentUser.uid).get();
                if (userDoc.exists) {
                    const userData = userDoc.data();
                    userCredits = userData.credits || 0;
                    document.getElementById('user-credits').textContent = userCredits;
                    
                    // Check if user has sufficient credits
                    if (userCredits < 1) {
                        document.getElementById('insufficient-credits').classList.remove('hidden');
                        document.getElementById('submit-btn').disabled = true;
                        document.getElementById('submit-btn').classList.add('opacity-50', 'cursor-not-allowed');
                    }
                }
            } catch (error) {
                console.error('Error loading user data:', error);
            }
        }

        // Update contact info placeholder based on method
        document.getElementById('contact-method').addEventListener('change', function() {
            const method = this.value;
            const contactInfo = document.getElementById('contact-info');
            const contactLabel = document.getElementById('contact-label');
            const contactHelp = document.getElementById('contact-help');
            
            switch(method) {
                case 'email':
                    contactInfo.placeholder = 'Enter your email address';
                    contactInfo.type = 'email';
                    contactLabel.textContent = 'Email Address';
                    contactHelp.textContent = 'We\'ll send your completed song to this email';
                    break;
                case 'whatsapp':
                    contactInfo.placeholder = 'Enter your WhatsApp number (with country code)';
                    contactInfo.type = 'tel';
                    contactLabel.textContent = 'WhatsApp Number';
                    contactHelp.textContent = 'Include country code (e.g., +1234567890)';
                    break;
                case 'telegram':
                    contactInfo.placeholder = 'Enter your Telegram username';
                    contactInfo.type = 'text';
                    contactLabel.textContent = 'Telegram Username';
                    contactHelp.textContent = 'Your Telegram username (without @)';
                    break;
                default:
                    contactInfo.placeholder = 'Enter your contact information';
                    contactInfo.type = 'text';
                    contactLabel.textContent = 'Contact Information';
                    contactHelp.textContent = 'We\'ll send your completed song to this address';
            }
        });

        // Form submission
        document.getElementById('request-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            if (userCredits < 1) {
                alert('You need at least 1 credit to submit a request.');
                return;
            }
            
            showLoading(true);
            
            try {
                const formData = new FormData(e.target);
                const requestData = {
                    songTitle: formData.get('songTitle'),
                    songType: formData.get('songType'),
                    description: formData.get('description'),
                    mood: formData.get('mood'),
                    specialInstructions: formData.get('specialInstructions'),
                    contactMethod: formData.get('contactMethod'),
                    contactInfo: formData.get('contactInfo'),
                    userEmail: currentUser.email
                };
                
                // Create the request
                const requestRef = await db.collection('requests').add({
                    userId: currentUser.uid,
                    ...requestData,
                    status: 'pending',
                    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                    updatedAt: firebase.firestore.FieldValue.serverTimestamp()
                });
                
                // Deduct credit from user
                await db.collection('users').doc(currentUser.uid).update({
                    credits: firebase.firestore.FieldValue.increment(-1)
                });
                
                showLoading(false);
                showSuccessModal();
                
                // Reset form
                e.target.reset();
                
            } catch (error) {
                console.error('Error submitting request:', error);
                showLoading(false);
                alert('There was an error submitting your request. Please try again.');
            }
        });

        // Utility functions
        function showLoading(show = true) {
            const overlay = document.getElementById('loading-overlay');
            if (show) {
                overlay.classList.remove('hidden');
            } else {
                overlay.classList.add('hidden');
            }
        }

        function showSuccessModal() {
            document.getElementById('success-modal').classList.remove('hidden');
        }

        function closeSuccessModal() {
            document.getElementById('success-modal').classList.add('hidden');
            loadUserData(); // Refresh credits
        }

        // Logout function
        document.getElementById('logout-btn').addEventListener('click', async () => {
            try {
                await auth.signOut();
                window.location.href = 'index.html';
            } catch (error) {
                console.error('Error signing out:', error);
            }
        });
    </script>
</body>
</html>
