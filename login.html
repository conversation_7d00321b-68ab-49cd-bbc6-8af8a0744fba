<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Aifrobeats</title>
    <link rel="icon" href="images/mlogo.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    
    <!-- Firebase UI Auth CSS -->
    <link type="text/css" rel="stylesheet" href="https://www.gstatic.com/firebasejs/ui/6.0.1/firebase-ui-auth.css" />
</head>
<body class="min-h-screen flex items-center justify-center" style="background: linear-gradient(135deg, var(--hero-gradient-start), var(--hero-gradient-end));">
    
    <!-- Navigation Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-white bg-opacity-10 backdrop-blur-md">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.html" class="logo-container text-2xl font-bold text-transparent flex items-center">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="site-logo">
                    <span class="animated-gradient-text">Aifrobeats</span>
                </a>
            </div>
            <div class="flex items-center space-x-4">
                <a href="index.html" class="text-white hover:text-green-400 transition-colors">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
                <a href="signup.html" class="primary-button">
                    <i class="fas fa-user-plus mr-2"></i>Sign Up
                </a>
            </div>
        </nav>
    </header>

    <!-- Login Container -->
    <div class="w-full max-w-md mx-auto px-6 pt-24">
        <div class="bg-white bg-opacity-10 backdrop-blur-md rounded-2xl shadow-2xl p-8 border border-white border-opacity-20">
            
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-music text-white text-2xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-white mb-2">Welcome Back!</h1>
                <p class="text-gray-300">Sign in to access your music dashboard</p>
            </div>

            <!-- Benefits Reminder -->
            <div class="bg-green-500 bg-opacity-20 rounded-lg p-4 mb-6 border border-green-400 border-opacity-30">
                <h3 class="text-green-300 font-semibold mb-2 flex items-center">
                    <i class="fas fa-gift mr-2"></i>Your Account Benefits
                </h3>
                <ul class="text-sm text-green-200 space-y-1">
                    <li>• 3 Free Preview Credits</li>
                    <li>• Track Your Song Requests</li>
                    <li>• Download Your Custom Music</li>
                    <li>• Priority Customer Support</li>
                </ul>
            </div>

            <!-- Firebase UI Auth Container -->
            <div id="firebaseui-auth-container" class="mb-6"></div>

            <!-- Custom Login Form (Fallback) -->
            <div id="custom-login-form" class="space-y-4" style="display: none;">
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                    <input type="email" id="email" name="email" required 
                           class="w-full px-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="Enter your email">
                </div>
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-300 mb-2">Password</label>
                    <input type="password" id="password" name="password" required 
                           class="w-full px-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="Enter your password">
                </div>
                <button type="submit" id="login-btn" class="w-full primary-button py-3 text-lg font-semibold">
                    <i class="fas fa-sign-in-alt mr-2"></i>Sign In
                </button>
            </div>

            <!-- Error Message -->
            <div id="error-message" class="hidden bg-red-500 bg-opacity-20 border border-red-400 border-opacity-30 rounded-lg p-3 mb-4">
                <p class="text-red-300 text-sm"></p>
            </div>

            <!-- Success Message -->
            <div id="success-message" class="hidden bg-green-500 bg-opacity-20 border border-green-400 border-opacity-30 rounded-lg p-3 mb-4">
                <p class="text-green-300 text-sm"></p>
            </div>

            <!-- Footer Links -->
            <div class="text-center space-y-3">
                <p class="text-gray-400 text-sm">
                    Don't have an account? 
                    <a href="signup.html" class="text-green-400 hover:text-green-300 font-semibold">Sign up here</a>
                </p>
                <p class="text-gray-500 text-xs">
                    By signing in, you agree to our 
                    <a href="terms.html" class="underline hover:text-green-400">Terms of Service</a> and 
                    <a href="privacy.html" class="underline hover:text-green-400">Privacy Policy</a>
                </p>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
            <span class="text-gray-700">Signing you in...</span>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/ui/6.0.1/firebase-ui-auth.js"></script>

    <!-- Login Script -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCRlkgzE2FMFFywISBm4GO4pfZ5pUFql0o",
            authDomain: "aifrobeats-com.firebaseapp.com",
            projectId: "aifrobeats-com",
            storageBucket: "aifrobeats-com.firebasestorage.app",
            messagingSenderId: "1013326896271",
            appId: "1:1013326896271:web:95074e13e4d40adaa164fa",
            measurementId: "G-YCQLRG8L44"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        // Firebase UI configuration
        const uiConfig = {
            signInSuccessUrl: 'dashboard.html',
            signInOptions: [
                {
                    provider: firebase.auth.EmailAuthProvider.PROVIDER_ID,
                    requireDisplayName: false
                }
            ],
            tosUrl: 'terms.html',
            privacyPolicyUrl: 'privacy.html'
        };

        // Initialize Firebase UI
        const ui = new firebaseui.auth.AuthUI(auth);

        // Check if user is already signed in
        auth.onAuthStateChanged(async (user) => {
            if (user) {
                // User is signed in, redirect to dashboard
                showSuccess('Welcome back! Redirecting to your dashboard...');
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);
            } else {
                // User is not signed in, show login form
                ui.start('#firebaseui-auth-container', uiConfig);
            }
        });

        // Utility functions
        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.querySelector('p').textContent = message;
            errorDiv.classList.remove('hidden');
            setTimeout(() => errorDiv.classList.add('hidden'), 5000);
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('success-message');
            successDiv.querySelector('p').textContent = message;
            successDiv.classList.remove('hidden');
            setTimeout(() => successDiv.classList.add('hidden'), 5000);
        }

        function showLoading(show = true) {
            const overlay = document.getElementById('loading-overlay');
            if (show) {
                overlay.classList.remove('hidden');
            } else {
                overlay.classList.add('hidden');
            }
        }
    </script>
</body>
</html>
