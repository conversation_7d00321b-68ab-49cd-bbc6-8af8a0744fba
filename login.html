<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Aifrobeats</title>
    <link rel="icon" href="images/mlogo.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    
    <!-- Firebase UI Auth CSS -->
    <link type="text/css" rel="stylesheet" href="https://www.gstatic.com/firebasejs/ui/6.0.1/firebase-ui-auth.css" />
</head>
<body class="min-h-screen flex flex-col" style="background: linear-gradient(135deg, var(--hero-gradient-start), var(--hero-gradient-end));">
    
    <!-- Navigation Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-white bg-opacity-10 backdrop-blur-md">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.html" class="logo-container text-2xl font-bold text-transparent flex items-center">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="site-logo">
                    <span class="animated-gradient-text">Aifrobeats</span>
                </a>
            </div>
            <div class="flex items-center space-x-4">
                <a href="index.html" class="text-white hover:text-green-400 transition-colors">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
                <a href="signup.html" class="primary-button">
                    <i class="fas fa-user-plus mr-2"></i>Sign Up
                </a>
            </div>
        </nav>
    </header>

    <!-- Main Content Container -->
    <main class="flex-1 flex items-center justify-center px-6 py-24">
        <!-- Login Container -->
        <div class="w-full max-w-md">
            <div class="bg-white bg-opacity-10 backdrop-blur-md rounded-2xl shadow-2xl p-8 border border-white border-opacity-20">
            
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-music text-white text-2xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-white mb-2">Welcome Back!</h1>
                <p class="text-gray-300">Sign in to access your music dashboard</p>
            </div>

            <!-- Benefits Reminder -->
            <div class="bg-green-500 bg-opacity-20 rounded-lg p-4 mb-6 border border-green-400 border-opacity-30">
                <h3 class="text-green-300 font-semibold mb-2 flex items-center">
                    <i class="fas fa-gift mr-2"></i>Your Account Benefits
                </h3>
                <ul class="text-sm text-green-200 space-y-1">
                    <li>• 3 Free Preview Credits</li>
                    <li>• Track Your Song Requests</li>
                    <li>• Download Your Custom Music</li>
                    <li>• Priority Customer Support</li>
                </ul>
            </div>

            <!-- Firebase UI Auth Container -->
            <div id="firebaseui-auth-container" class="mb-6"></div>

            <!-- Custom Login Form (Fallback) -->
            <div id="custom-login-form" class="space-y-4" style="display: none;">
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                    <input type="email" id="email" name="email" required 
                           class="w-full px-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="Enter your email">
                </div>
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-300 mb-2">Password</label>
                    <input type="password" id="password" name="password" required 
                           class="w-full px-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="Enter your password">
                </div>
                <button type="submit" id="login-btn" class="w-full primary-button py-3 text-lg font-semibold">
                    <i class="fas fa-sign-in-alt mr-2"></i>Sign In
                </button>
            </div>

            <!-- Error Message -->
            <div id="error-message" class="hidden bg-red-500 bg-opacity-20 border border-red-400 border-opacity-30 rounded-lg p-3 mb-4">
                <p class="text-red-300 text-sm"></p>
            </div>

            <!-- Success Message -->
            <div id="success-message" class="hidden bg-green-500 bg-opacity-20 border border-green-400 border-opacity-30 rounded-lg p-3 mb-4">
                <p class="text-green-300 text-sm"></p>
            </div>

            <!-- Footer Links -->
            <div class="text-center space-y-3">
                <p class="text-gray-400 text-sm">
                    Don't have an account?
                    <a href="signup.html" class="text-green-400 hover:text-green-300 font-semibold">Sign up here</a>
                </p>
                <p class="text-gray-400 text-sm">
                    Forgot your password?
                    <button id="reset-password-btn" class="text-green-400 hover:text-green-300 font-semibold underline">Reset it here</button>
                </p>
                <p class="text-gray-500 text-xs">
                    By signing in, you agree to our
                    <a href="terms.html" class="underline hover:text-green-400">Terms of Service</a> and
                    <a href="privacy.html" class="underline hover:text-green-400">Privacy Policy</a>
                </p>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-gray-400 py-6">
        <div class="container mx-auto px-6 text-center">
            <div class="flex items-center justify-center mb-4">
                <img src="images/mlogo.png" alt="Aifrobeats Logo" class="w-6 h-6 mr-2">
                <span class="animated-gradient-text text-lg font-semibold">Aifrobeats</span>
            </div>
            <p class="text-sm mb-4">AI-Generated Afrobeats, Human-Curated for you.</p>
            <div class="flex justify-center space-x-4 text-xs">
                <a href="terms.html" class="hover:text-green-400 transition-colors">Terms of Service</a>
                <span>|</span>
                <a href="privacy.html" class="hover:text-green-400 transition-colors">Privacy Policy</a>
                <span>|</span>
                <a href="index.html" class="hover:text-green-400 transition-colors">Back to Home</a>
            </div>
            <p class="text-xs mt-4">&copy; <span id="currentYear"></span> AIFROBEATS - All Rights Reserved.</p>
        </div>
    </footer>

    <!-- Password Reset Modal -->
    <div id="reset-password-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 px-4">
        <div class="bg-white rounded-lg p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800">Reset Password</h3>
                <button id="close-reset-modal" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                <p class="text-yellow-800 text-sm">
                    <i class="fas fa-info-circle mr-2"></i>
                    This email already exists without any means of sign-in. Please reset the password to recover.
                </p>
            </div>
            <p class="text-gray-600 text-sm mb-4">
                Get instructions sent to this email that explain how to reset your password
            </p>
            <div class="mb-4">
                <label for="reset-email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                <input type="email" id="reset-email"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                       placeholder="Enter your email address">
            </div>
            <div class="flex space-x-3">
                <button id="cancel-reset" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    Cancel
                </button>
                <button id="send-reset" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    Send
                </button>
            </div>
            <div class="mt-4 text-xs text-gray-500 space-x-2">
                <a href="terms.html" class="hover:text-green-600">Terms of Service</a>
                <a href="privacy.html" class="hover:text-green-600">Privacy Policy</a>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
            <span class="text-gray-700">Signing you in...</span>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/ui/6.0.1/firebase-ui-auth.js"></script>

    <!-- Login Script -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCRlkgzE2FMFFywISBm4GO4pfZ5pUFql0o",
            authDomain: "aifrobeats-com.firebaseapp.com",
            projectId: "aifrobeats-com",
            storageBucket: "aifrobeats-com.firebasestorage.app",
            messagingSenderId: "1013326896271",
            appId: "1:1013326896271:web:95074e13e4d40adaa164fa",
            measurementId: "G-YCQLRG8L44"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        // Firebase UI configuration
        const uiConfig = {
            signInSuccessUrl: 'dashboard.html',
            signInOptions: [
                {
                    provider: firebase.auth.EmailAuthProvider.PROVIDER_ID,
                    requireDisplayName: false,
                    signInMethod: firebase.auth.EmailAuthProvider.EMAIL_PASSWORD_SIGN_IN_METHOD
                }
            ],
            tosUrl: 'terms.html',
            privacyPolicyUrl: 'privacy.html',
            callbacks: {
                signInSuccessWithAuthResult: function(authResult, redirectUrl) {
                    // User successfully signed in
                    showSuccess('Welcome back! Redirecting to your dashboard...');
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1500);
                    return false; // Avoid redirects after sign-in.
                },
                signInFailure: function(error) {
                    // Handle sign-in errors
                    console.error('Sign-in error:', error);

                    if (error.code === 'firebaseui/anonymous-upgrade-merge-conflict') {
                        showError('This email is already associated with an account. Please sign in with your existing credentials.');
                    } else if (error.code === 'auth/user-not-found') {
                        showError('No account found with this email. Please sign up first.');
                    } else if (error.code === 'auth/wrong-password') {
                        showError('Incorrect password. Please try again or reset your password.');
                    } else if (error.code === 'auth/invalid-email') {
                        showError('Please enter a valid email address.');
                    } else if (error.code === 'auth/user-disabled') {
                        showError('This account has been disabled. Please contact support.');
                    } else if (error.code === 'auth/too-many-requests') {
                        showError('Too many failed attempts. Please try again later.');
                    } else if (error.message && error.message.includes('already exists without any means of sign-in')) {
                        // Show the reset password modal for this specific case
                        const email = error.email || '';
                        document.getElementById('reset-email').value = email;
                        document.getElementById('reset-password-modal').classList.remove('hidden');
                    } else {
                        showError('Sign-in failed: ' + (error.message || 'Please try again.'));
                    }

                    return Promise.resolve();
                }
            }
        };

        // Initialize Firebase UI
        const ui = new firebaseui.auth.AuthUI(auth);

        // Check if user is already signed in
        auth.onAuthStateChanged(async (user) => {
            if (user) {
                console.log('User already signed in:', user.uid);

                // Check if user document exists and create if missing
                try {
                    const userDoc = await db.collection('users').doc(user.uid).get();
                    if (!userDoc.exists) {
                        console.log('User document missing, creating...');
                        await db.collection('users').doc(user.uid).set({
                            email: user.email,
                            credits: 3,
                            createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                            requests: [],
                            profile: {
                                displayName: user.displayName || '',
                                photoURL: user.photoURL || ''
                            }
                        });
                        console.log('User document created for existing user');
                    }
                } catch (error) {
                    console.error('Error checking/creating user document:', error);
                }

                // User is signed in, redirect to dashboard
                showSuccess('Welcome back! Redirecting to your dashboard...');
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);
            } else {
                // User is not signed in, show login form
                ui.start('#firebaseui-auth-container', uiConfig);
            }
        });

        // Utility functions
        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.querySelector('p').textContent = message;
            errorDiv.classList.remove('hidden');
            setTimeout(() => errorDiv.classList.add('hidden'), 5000);
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('success-message');
            successDiv.querySelector('p').textContent = message;
            successDiv.classList.remove('hidden');
            setTimeout(() => successDiv.classList.add('hidden'), 5000);
        }

        function showLoading(show = true) {
            const overlay = document.getElementById('loading-overlay');
            if (show) {
                overlay.classList.remove('hidden');
            } else {
                overlay.classList.add('hidden');
            }
        }

        // Password Reset Modal functionality
        const resetPasswordBtn = document.getElementById('reset-password-btn');
        const resetPasswordModal = document.getElementById('reset-password-modal');
        const closeResetModal = document.getElementById('close-reset-modal');
        const cancelReset = document.getElementById('cancel-reset');
        const sendReset = document.getElementById('send-reset');
        const resetEmailInput = document.getElementById('reset-email');

        // Show reset password modal
        resetPasswordBtn.addEventListener('click', () => {
            resetPasswordModal.classList.remove('hidden');
        });

        // Hide reset password modal
        function hideResetModal() {
            resetPasswordModal.classList.add('hidden');
            resetEmailInput.value = '';
        }

        closeResetModal.addEventListener('click', hideResetModal);
        cancelReset.addEventListener('click', hideResetModal);

        // Send password reset email
        sendReset.addEventListener('click', async () => {
            const email = resetEmailInput.value.trim();

            if (!email) {
                showError('Please enter your email address.');
                return;
            }

            if (!email.includes('@')) {
                showError('Please enter a valid email address.');
                return;
            }

            try {
                showLoading(true);
                await auth.sendPasswordResetEmail(email);
                hideResetModal();
                showSuccess('Password reset email sent! Check your inbox and follow the instructions.');
            } catch (error) {
                console.error('Password reset error:', error);

                if (error.code === 'auth/user-not-found') {
                    showError('No account found with this email address.');
                } else if (error.code === 'auth/invalid-email') {
                    showError('Please enter a valid email address.');
                } else if (error.code === 'auth/too-many-requests') {
                    showError('Too many requests. Please try again later.');
                } else {
                    showError('Failed to send reset email. Please try again.');
                }
            } finally {
                showLoading(false);
            }
        });

        // Handle Enter key in reset email input
        resetEmailInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendReset.click();
            }
        });

        // Set current year in footer
        document.getElementById('currentYear').textContent = new Date().getFullYear();
    </script>
</body>
</html>
